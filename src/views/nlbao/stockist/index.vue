<template>
  <div class="app-container">
    <Form
      :showSearch="showSearch"
      :searchList="searchList"
      @getList="getList"
    ></Form>
    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>
     <el-table :data="tableData" @selection-change="handleSelectionChange" v-loading="loading">
      <!-- <el-table-column align="center" type="selection" width="55" /> -->
      <el-table-column align="center" label="编号" prop="roleId" width="50" fixed />
      <el-table-column
        fixed
        align="center"
        label="操作"
        :show-overflow-tooltip="true"
        width="80"
      >
      <template slot-scope="scope">
        <el-button size="mini" type="text" @click="handelDetail(scope.row)">详情</el-button>
      </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="牧民姓名"
        prop="userName"
        :show-overflow-tooltip="true"
      />
       <el-table-column
        align="center"
        label="联系电话"
        prop="userPhone"
        :show-overflow-tooltip="true"
      >
      </el-table-column>
       <!-- <el-table-column
        align="center"
        label="养殖总数量"
        prop="numOfBreeding"
        :show-overflow-tooltip="true"
        width="200"
      > 
      </el-table-column>-->
      <el-table-column
        align="center"
        label="自有草场（亩）"
        prop="ownPasture"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        align="center"
        label="租用草场（亩）"
        prop="rentPasture"
        :show-overflow-tooltip="true"
      />
       <!-- <el-table-column
        align="center"
        label="棚圈（间）"
        prop="shedNum"
        :show-overflow-tooltip="true"
        width="150"
      />
       <el-table-column
        align="center"
        label="棚圈总价值（万）"
        prop="shedAmtWan"
        :show-overflow-tooltip="true"
        width="150"
      />
      <el-table-column
        align="center"
        label="房屋（平米）"
        prop="housesArea"
      />
      <el-table-column
        align="center"
        label="房屋价值（万）"
        prop="housesAmtWan"
      />
      <el-table-column
        align="center"
        label="生产机械（台）"
        prop="prodMachinery"
        width="120"
      >
      </el-table-column>
      <el-table-column
        align="center"
        label="生产机械总价值（万）"
        prop="prodMachineryAmtWan"
        width="120"
      >
      </el-table-column> -->
      <el-table-column
        align="center"
        label="处理状态"
        prop="approvalStatus"
        width="120"
      >
      <template slot-scope="scope">
        <el-tag :type="scope.row.approvalStatus == 1 ? 'success': 'default'">
          <span>{{scope.row.approvalStatus | alreadyStatus}}</span>
        </el-tag>
        
      </template>
      </el-table-column>
      <!-- <el-table-column
        align="center"
        label="审核状态"
        prop="approvalStatus"
        width="120"
      >
      <template slot-scope="scope">
        <span>{{scope.row.approvalStatusName}}</span>
      </template>
      </el-table-column> -->
    </el-table>
    <pagination
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    >
    </pagination>
  </div>
</template>

<script>
import Form from "../../../views/nlb/components/form.vue"
import { SecuredList } from '@/api/nlbao/guarantee'

export default {
  dicts: ["sys_normal_disable"],
  data() {
    return {
      showSearch: true,
      searchList: [
        { name: "牧民姓名", value: "userName", type: "text" },
        { name: "牧民手机号", value: "userPhone", type: "text" },
        {
          name: "处理状态",
          value: "approvalStatus",
          type: "select",
          options: [
            { label: "全部", value: 0 },
            { label: "待处理", value: 1 },
            { label: "已处理", value: "5 ,6" }
          ]
        },
        /* {
          name: "审核状态",
          value: "approvalStatus",
          type: "select",
          options: [
            { label: "全部", value: 0 },
            { label: "等待畜牧书完善信息", value: 1 },
            { label: "等待平台审核", value: 2 },
            { label: "等待推荐企业审核", value: 3 },
            { label: "等待银行审核", value: 4 }
          ]
        } */
      ],
      //   loading: true,
      // 表格数据
      tableData: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      searchParams:{},
      extsysUserInfo: {}
    }
  },
  components: {
    Form
  },
    filters: {            
    // 状态 (0取消 1提交成功，2畜牧师成功，3运营人成功，4推荐企业成功，5银行成功)
    alreadyStatus: (type) =>{
      const statusMap = {
        0: '已处理',
        1: '待处理',
        2: '已处理',
        3: '已处理',
        4: '已处理',
        5: '已处理',
        6: '已处理'
      }
      return statusMap[type]
    }
  },
  created() {
    this.getList();
    this.extsysUserInfo = JSON.parse(window.localStorage.getItem('extsysUserInfo'))
  },
  methods: {
        getList(data) {
          const status = '1,2,3,4,5,6'
          const approvalStatus = data?.approvalStatus ? data?.approvalStatus: status
          if(data) {
            this.searchParams = {
             userName : data.userName,
             userPhone: data.userPhone,
             userIdNo: data.userIdNo,
             companyName: data.companyName,
             bankName: data.bankName,
            }
          }
          this.loading = true;
           SecuredList({...this.searchParams, ...this.queryParams, approvalStatusArray: approvalStatus, pastorId: this.extsysUserInfo.access_token}).then(res => {
              this.loading = false;
              this.tableData = res.data.list;
              this.total = res.data.totalRow;
              this.tableData.map((item, index) => {
                item.roleId = index + 1;
                return index
              })
           })
        },
      exportRecords() {
        console.log("exportRecords")
      },
    exportExcel() {
      console.log("exportExcel")
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      console.log(selection)
      this.ids = selection.map((item) => item.roleId)
      this.single = selection.length != 1
      this.multiple = !selection.length
    },
    handelDetail(row){
      console.log(row.id)
      this.$router.push({
        path: '/stockist/detail',
        query: {
          id: row.quotaId,
          type: 0
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped></style>
