<template>
  <div class="app-container">
    <Form
      :showSearch="showSearch"
      :searchList="searchList"
      @getList="getList"
    ></Form>
    <el-row :gutter="10" class="mb8">
      <!-- <el-col :span="1.5">
        <el-button
          icon="el-icon-download"
          type="primary"
          size="mini"
          @click="exportRecords"
          >导出记录</el-button
        >
        <el-button
          icon="el-icon-download"
          type="warning"
          size="mini"
          @click="exportExcel"
          >导出excel</el-button
        >
      </el-col> -->
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>
    <el-table :data="tableData" @selection-change="handleSelectionChange" highlight-current-row v-loading="loading">
      <el-table-column align="center" type="selection" width="55" />
      <el-table-column align="center" label="编号" prop="roleId" width="50" fixed />
      <el-table-column
        fixed
        align="center"
        label="操作"
        :show-overflow-tooltip="true"
        width="150"
      >
      <template slot-scope="scope">
        <el-button v-if="scope.row.approvalStatus == 3 && scope.row.type == 1" size="mini" type="text" @click="handeSet(scope.row)">审核</el-button>
        <el-button size="mini" type="text" @click="handelDetail(scope.row)">详情</el-button>
       <el-button size="mini" type="text" v-if="scope.row.approvalStatus == 7"  @click="handelRepay(scope.row)">还款记录</el-button>
        <el-button size="mini" type="text" v-if="scope.row.approvalStatus == 7" @click="handelpayInfo(scope.row)">消费记录</el-button>
      </template>
      </el-table-column>
      <el-table-column align="center" label="申请时间" prop="createTime" width="160" />
      <el-table-column align="center" label="申请人" prop="userName" :show-overflow-tooltip="true" width="150" />
      <el-table-column align="center" label="联系电话" prop="userPhone" :show-overflow-tooltip="true" width="150" ></el-table-column>
      <el-table-column align="center" label="经营区域" width="180" >
        <template slot-scope="scope">
          <span>{{scope.row.businessPremisesName}}{{scope.row.address}}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="申请主体" prop="userTypeName" :show-overflow-tooltip="true" width="130" />
      <el-table-column align="center" label="申请方式" prop="applyTypeName" :show-overflow-tooltip="true" width="130" />
      <el-table-column align="center" label="额度类型" prop="typeName" :show-overflow-tooltip="true" width="130" >
      </el-table-column>
      <el-table-column align="center" width="150" label="申请额度(万)" prop="applyAmtWan" :show-overflow-tooltip="true"/>
      <el-table-column align="center" label="推荐企业" prop="companyName" :show-overflow-tooltip="true" width="150"/>
      <el-table-column align="center" label="推荐人名称" prop="referrer" :show-overflow-tooltip="true" width="150"/>
      <!-- <el-table-column
        align="center"
        label="申请人姓名"
        prop="userName"
        :show-overflow-tooltip="true"
        width="150"
      />
       <el-table-column
        align="center"
        label="联系电话"
        prop="userPhone"
        :show-overflow-tooltip="true"
        width="150"
      >
      </el-table-column>
       <el-table-column
        align="center"
        label="身份证号"
        prop="userIdNo"
        :show-overflow-tooltip="true"
        width="200"
      >
      </el-table-column>
      <el-table-column
        align="center"
        label="申请方式"
        prop="applyTypeName"
        :show-overflow-tooltip="true"
        width="130"
      />
      <el-table-column
        align="center"
        label="申请主体"
        prop="userTypeName"
        :show-overflow-tooltip="true"
        width="130"
      />
      <el-table-column
        align="center"
        label="额度类型"
        prop="typeName"
        :show-overflow-tooltip="true"
        width="130"
     />
      <el-table-column
        align="center"
        label="申请额度(元)"
        prop="applyAmt"
        :show-overflow-tooltip="true"
        width="150"
      />
      <el-table-column
        align="center"
        label="申请额度(元)"
        prop="applyAmt"
        :show-overflow-tooltip="true"
        width="150"
      />
      <el-table-column
        align="center"
        label="申请期限(月)"
        prop="quotaTerm"
        :show-overflow-tooltip="true"
        width="150"
      />
       <el-table-column
        align="center"
        label="推荐企业"
        prop="companyName"
        :show-overflow-tooltip="true"
        width="150"
      />
      <el-table-column
        align="center"
        label="放款单号"
        prop="quotaCode"
        :show-overflow-tooltip="true"
        width="150"
      >
      <template slot-scope="scope">
        <span v-if="scope.row.approvalStatus ==7">{{scope.row.quotaCode}}</span>
      </template>
      </el-table-column>
       <el-table-column
        align="center"
        label="放款银行"
        prop="bankName"
        :show-overflow-tooltip="true"
        width="150"
      />
      <el-table-column
        align="center"
        label="放款金额(元)"
        prop="actualAmt"
      />
      <el-table-column
        align="center"
        label="放款期限(月)"
        prop="bankTerm"
      />
      <el-table-column
        align="center"
        label="利率（年化）"
        prop="quotaRate"
        width="120"
      >
      </el-table-column>
      <el-table-column
        align="center"
        label="申请时间"
        prop="createTime"
        width="120"
      >
      </el-table-column>
      <el-table-column
        align="center"
        label="经营区域"
        width="120"
      >
      <template slot-scope="scope">
        <span>{{scope.row.businessPremisesName}}{{scope.row.address}}</span>
      </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="处理状态"
        prop="approvalStatus"
        width="120"
      >
      <template slot-scope="scope">
        <span>{{scope.row.approvalStatus | alreadyStatus}}</span>
      </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="审核状态"
        prop="approvalStatus"
        width="120"
      >
      <template slot-scope="scope">
       <div>
          <div>{{scope.row.approvalStatusName}}</div>
       </div>
      </template>
      </el-table-column> -->
    </el-table>
    <pagination
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    >
    </pagination>
  </div>
</template>

<script>
import Form from "@/views/nlb/components/form.vue"
import { SecuredList } from '@/api/nlbao/guarantee'
export default {
  dicts: ["sys_normal_disable"],
  data() {
    return {
      showSearch: true,
      // searchList: [
      //   { name: "申请人姓名", value: "userName", type: "text" },
      //   { name: "申请人手机号", value: "userPhone", type: "text" },
      //   { name: "申请人身份证号", value: "userIdNo", type: "text" },
      //   { name: "服务店铺名称", value: "companyName", type: "text" },
      //   { name: "放款银行", value: "bankName", type: "text" },
      //    {
      //     name: "处理状态",
      //     value: "status",
      //     type: "select",
      //     options: [
      //       { label: "待处理", value: '3' },
      //       { label: "已处理", value: '4,6,7'}
      //     ]
      //   },
      //   {
      //     name: "审核状态",
      //     value: "approvalStatus",
      //     type: "select",
      //     options: [
      //       { label: "全部", value: 0 },
      //       { label: "等待服务店铺审核", value: 3 },
      //       { label: "等待银行审核", value: 4 },
      //       { label: "等待平台确认", value: 5 },
      //       { label: "审核成功", value: 7 },
      //       { label: "审核失败", value: 6 },
      //     ]
      //   }
      // ],

      searchList: [
        {
          name: "审核状态",
          value: "approvalStatus",
          type: "select",
          options: [
            { label: "全部", value: 0 },
            { label: "等待平台审核", value: 2 },
            { label: "等待推荐企业审核", value: 3 },
            { label: "等待银行审核", value: 4 },
            { label: "等待平台确认", value: 5 },
            { label: "审核成功", value: 7 },
            { label: "审核失败", value: 6 },
          ]
        },
        { name: "申请人", value: "userName", type: "text" },
        { name: "联系电话", value: "userPhone", type: "text" },
        { name: "申请时间", value: "createTime", type: "daterange" },
        {
          name: "申请主体",
          value: "userType",
          type: "select",
          options: [
            { label: "全部", value: '' },
            { label: "个人", value: 3 },
            { label: "企业", value: 1 }
          ]
        },
        {
          name: "申请方式",
          value: "applyType",
          type: "select",
          options: [
            { label: "全部", value: '' },
            { label: '活畜抵押', value: 1 },
            { label: '信用额度', value: 2 },
            { label: '企业推荐', value: 4 },
          ]
        },
        {
          name: "额度类型",
          value: "type",
          type: "select",
          options: [
            { label: "全部", value: '' },
            { label: '专项额度', value: 1 },
            { label: '通用额度', value: 2 }
          ]
        },
        { name: "推荐企业", value: "companyName", type: "text" },
        { name: "推荐人名称", value: "referrer", type: "text" },
      ],
      loading: true,
      // 表格数据
      tableData: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      searchParams: {},
      searchstatus:'',
      extsysUserInfo: {}
    }
  },
  components: {
    Form,
  },
  filters: {            
    // 状态 (0取消 1提交成功，2畜牧师成功，3运营人成功，4服务店铺成功，5银行成功)
    alreadyStatus: (type) =>{
      const statusMap = {
        0: '已处理',
        1: '已处理',
        2: '已处理',
        3: '待处理',
        4: '已处理',
        5: '已处理',
        6: '已处理',
        7: '已处理'
      }
      return statusMap[type]
    }
  },
  created() {
    this.extsysUserInfo = JSON.parse(window.localStorage.getItem('extsysUserInfo'))
    this.getList();
  },
  methods: {
   async getList(data) {
    /*  const status = '3,4,5,6,7'
     const approvalStatus = data?.approvalStatus ? data?.approvalStatus: status	 */
      if(data?.approvalStatus){
          this.searchstatus = data.approvalStatus
        } else if(data?.status) {
            this.searchstatus = data.status
        } else if(data?.approvalStatus && data?.status) {
          this.searchstatus = data.approvalStatus
        } else {
            this.searchstatus = '3,4,5,6,7'
        }
     if(data) {
       this.searchParams = {
              userName : data.userName,
              userPhone: data.userPhone,
              userIdNo: data.userIdNo,
              companyName: data.companyName,
              bankName: data.bankName,
              startTime: data.createTime && data.createTime[0],
              endTime: data.createTime && data.createTime[1], 
              referrer: data.referrer,
              type: data.type,
              applyType: data.applyType
       }
     }
      this.loading = true;
      const res = await SecuredList({...this.searchParams,...this.queryParams, approvalStatusArray: this.searchstatus,
                companyUserId: this.extsysUserInfo.access_token});
       if(res.stautscode ===200) {
        this.loading = false;
        this.tableData = res.data.list;
        this.total = res.data.totalRow;
        this.tableData.map((item, index) => {
          item.roleId = index + 1;
          item.userTypeName = item.userType == 1 ? '企业' : '个人'
          // item.applyTypeName = item.applyType == 1 ? '抵押' : '企业推荐'
          item.typeName = item.type == 1 ? '专项额度' : '通用额度'
          item.applyAmtWan = item.applyAmt / 10000;
          return index
        })
      } else {
        this.$message.error(res.msg)
      }
    },
   /*  exportRecords() {
        console.log('exportRecords')
    },
    exportExcel(){
        console.log('exportExcel')
    },
     */
    // 多选框选中数据
    handleSelectionChange(selection) {
      console.log(selection)
      this.ids = selection.map((item) => item.roleId)
      this.single = selection.length != 1
      this.multiple = !selection.length
    },
    handeSet(row){
      console.log(row);
       this.$router.push({
        path: '/guarantor/examine',
        query: {
          id: row.quotaId,
          type: 1
        }
      })
    },
    handelDetail(row){
      console.log(row.id)
      this.$router.push({
        path: '/guarantor/examine',
        query: {
          id: row.quotaId,
          type: 0
        }
      })
    },
    // 还款记录
    handelRepay(row) {
      console.log(row)
      this.$router.push({
        path: '/guarantor/guarmentInfo',
        query: {
          id: row.quotaId,
          type: 0,
          quotaCode: row.quotaCode
        }
      })
    },
    //  消费记录
    handelpayInfo(row){
      console.log(row)
       this.$router.push({
        path: '/guarantor/guarpayinfo',
        query: {
          id: row.quotaId,
          type: 1,
          quotaCode: row.quotaCode
        }
      })
    },
  }
}
</script>
<style lang="scss" scoped>
</style>
