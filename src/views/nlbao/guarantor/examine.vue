<template>
  <div class="app-container">
    <div class="table">
      <div class="step_style" v-if="type==0">
     <el-alert
         v-if="detailInfo.approvalStatus == 6"
        :title="detailInfo.approvalStatusName"
        type="warning"
        :description="detailInfo.reason"
        :closable="false">
      </el-alert>
       <el-alert
        v-else
        :title="detailInfo.approvalStatusName"
        type="warning"
        :closable="false">
      </el-alert>
      </div>
      <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-width="150px"
      class="demo-ruleForm"
      v-if="type ==1"
    >
      <el-form-item label="申请人申请额度">
        <div>{{detailInfo.applyAmt}}</div>
      </el-form-item>
      <el-form-item label="平台评估额度">
        <div>{{detailInfo.platformAmt}}</div>
      </el-form-item>

      <el-form-item label="审核意见" prop="approvalStatus">
        <el-radio-group v-model="ruleForm.approvalStatus">
          <el-radio :label="4">审核通过</el-radio>
          <el-radio :label="6">审核不通过</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="" prop="reason" v-if="ruleForm.approvalStatus ==6">
        <el-input
          type="textarea"
          v-model="ruleForm.reason"
          style="width: 50%"
          placeholder="请输入不通过原因"
        ></el-input>
      </el-form-item>
      <!-- <el-form-item label="推荐企业评估额度" prop="guarAmt" v-if="ruleForm.approvalStatus ==4">
        <el-input
          v-model="ruleForm.guarAmt"
          type='number'
          style="width: 50%"
          oninput="if(value.length > 8) value=value.slice(0, 8)"
          placeholder="推荐企业评估额度"
        ></el-input>
        元
      </el-form-item> -->
       <el-form-item >
        <el-button type="primary" @click="handelSubmitForm">提交</el-button>
      </el-form-item>
    </el-form>
    <div class="is_show" @click="handelShow" v-if="type ==1">{{showTitle}}
    <i :class="icon"></i>
    </div>
    <div style="display:none">
      <el-button @click="handelEdit" v-if="!disabled">编辑</el-button>
      <el-button type="primary" @click="handelSubmit" v-if="disabled">保存</el-button>
      <el-button  @click="cancel" v-if="disabled">取消</el-button>
    </div>
    
    <div v-show="isShow">
      <el-descriptions title="放款信息" border v-if="type==0" :contentStyle="C_S">
        <el-descriptions-item label="放款单号">
          <span v-if="detailInfo.approvalStatus==7">{{detailInfo.quotaCode}}</span>
        </el-descriptions-item>
        <el-descriptions-item label="受理银行">{{detailInfo.bankName}}</el-descriptions-item>
        <el-descriptions-item label="放款金额（元）">{{detailInfo.actualAmt}}</el-descriptions-item>
        <el-descriptions-item label="放款期限（月）">{{detailInfo.bankTerm}}</el-descriptions-item>
        <el-descriptions-item label="利率（年化）">{{detailInfo.quotaRate}}</el-descriptions-item>
        <el-descriptions-item label="日期范围">{{detailInfo.quotaStartTime}} ～ {{detailInfo.quotaEndTime}}</el-descriptions-item>
        <!-- <el-descriptions-item label="银行地址">{{detailInfo.bankAddress}}</el-descriptions-item>
        <el-descriptions-item label="银行电话">{{detailInfo.bankContactPhone}}</el-descriptions-item>
        <el-descriptions-item label="信贷员">信贷员</el-descriptions-item> -->
      </el-descriptions>
     <!--  <template v-if="detailInfo.approvalStatus != 4"> -->
       <template>
        <el-descriptions title="银行评估" border v-if="type==0" :contentStyle="CS">
        <el-descriptions-item label="银行评估额度（元）">{{detailInfo.bankAmt}}</el-descriptions-item>
      </el-descriptions>
      <!-- <el-descriptions title="推荐企业评估" border v-if="type==0" :contentStyle="CS">
        <el-descriptions-item label="推荐企业评估额度（元）">{{detailInfo.guarAmt}}</el-descriptions-item>
      </el-descriptions> -->
      </template>
       <el-descriptions title="平台评估" border v-if="type==0" :contentStyle="CS">
        <el-descriptions-item label="平台评估额度（元）">{{detailInfo.platformAmt}}</el-descriptions-item>
        <el-descriptions-item label="分配银行">{{detailInfo.bankName}}</el-descriptions-item>
      </el-descriptions>
      <el-descriptions title="推荐企业信息" border v-if="detailInfo.guarId">
        <el-descriptions-item label="推荐企业名称">{{detailInfo.companyName}}</el-descriptions-item>
        <el-descriptions-item label="法人">{{detailInfo.corprateName}}</el-descriptions-item>
        <el-descriptions-item label="企业代码">{{detailInfo.certNo}}</el-descriptions-item>
      </el-descriptions>

      <el-descriptions title="申请信息" border>
        <el-descriptions-item label="申请人姓名">{{detailInfo.userName}}</el-descriptions-item>
        <el-descriptions-item label="联系电话">{{detailInfo.userPhone}}</el-descriptions-item>
        <el-descriptions-item label="身份证号">{{detailInfo.userIdNo}}</el-descriptions-item>
        <el-descriptions-item label="申请额度（元）">{{detailInfo.applyAmt}}</el-descriptions-item>
        <el-descriptions-item label="申请期限（月）">{{detailInfo.quotaTerm}}</el-descriptions-item>
        <!-- </el-descriptions-item> -->
        <el-descriptions-item label="资金用途">{{detailInfo.quotaPurpose}}</el-descriptions-item>
        <el-descriptions-item label="申请时间">{{detailInfo.createTime}}</el-descriptions-item>
          <el-descriptions-item label="负债金额">{{
            detailInfo.debtAmt
          }}</el-descriptions-item>
          <el-descriptions-item label="负债说明">{{
            detailInfo.debtExplain
          }}</el-descriptions-item>
      </el-descriptions>
    
     <div class="user_info">
        <el-descriptions title="身份信息" border>
            <el-descriptions-item label="婚姻状况">{{detailInfo.maritalStatus | formartSex}}</el-descriptions-item>
            <el-descriptions-item v-if="detailInfo.maritalStatus == 2" label="配偶姓名">{{detailInfo.spouseName}}</el-descriptions-item>
            <el-descriptions-item v-if="detailInfo.maritalStatus == 2" label="配偶身份证号">{{detailInfo.spouseIdNo}}</el-descriptions-item>
        </el-descriptions>


        
        <div class="user_card" v-if="faImageList.length > 0">
          <span class="user_title">申请方证件信息</span>
          <div class="images">
            <div class="view-images">
              <div
                class="view-images-list"
                v-for="(imgItem, index) in faImageList"
                :key="index"
              >
                <img :src="imgItem.url" alt="" @click="showimage(imgItem)" />
              </div>
            </div>
        </div> 
        div class="user_card">
            <span class="user_title">配偶身份证信息</span>
             <div class="images">
               <el-image style="width:150px; height:150px" :src="detailInfo.spouseIdPic1"></el-image>
                <el-image style="width:150px; height:150px" :src="detailInfo.spouseIdPic2"></el-image>
            </div>
            <div class="images" >
                <div class="view-images" v-if="wifeList.length>0">
						    	<div class="view-images-list" v-for="(imgItem,index) in wifeList" :key="index">
								  <!-- <div class="delete-img" v-if="disabled" @click="deleteWifeImg(index, wifeList)"><i class="el-icon-error"></i></div> -->
								<img :src="imgItem.url" alt="" @click="showWifeImage(imgItem)">
							</div>
              <!-- <el-upload
                  v-if="wifeFlag"
                  :action="uploadImgUrl"
                  list-type="picture-card"
                  :on-preview="handlePictureCardPreview"
                  :file-list="wifeList"
						    	value-key="img_url"
                  :limit="2"
                  :auto-upload="true"
                  :show-file-list="false"
                  :on-success="handlWifeSuccess"
                  :headers="headers"
						>
							<i class="el-icon-plus"></i>
						</el-upload> -->
						</div>
            </div>
        </div> 
       </div>

      <!-- <el-descriptions title="征信信息" border direction="vertical">
          <el-descriptions-item label="申请人征信信息" :span="3">
              <div class="images" style="display:flex">
                <div class="view-images">
                  <div v-for="(item,index) in detailInfo.quotaFileList" :key="item.id">
                    <div class="view-images-list" v-if="item.fileType == 1">
                      <div class="delete-img" v-if="commonFlay" @click="deleteApply(index, detailInfo.quotaFileList)"><i class="el-icon-error"></i></div>
                      <img v-if="item.fileType == 1" style="width:150px; height:150px;margin-right:10px" :src="item.filePath" alt="" @click="showApplyImage(item)">
                </div>
                  </div>
                 </div>  
                     <el-upload
                        v-if="commonFlay"
                        :action="uploadImgUrl"
                        list-type="picture-card"
                        :on-preview="handlePictureCardPreview"
                        :file-list="quotaFileList"
                        value-key="img_url"
                        :auto-upload="true"
                        :show-file-list="false"
                        :on-success="handlSuccessApply"
                        :headers="headers"
						>
							<i class="el-icon-plus"></i>
						</el-upload>
              </div> 
              <el-dialog :visible.sync="dialogVisible">
							<img width="100%" :src="dialogImageUrl" alt="" />
						</el-dialog>
          </el-descriptions-item>
          <el-descriptions-item label="配偶征信信息" :span="3">
              <div class="images" style="display:flex">
                <div class="view-images">
                  <div v-for="(item,index) in detailInfo.quotaFileList" :key="item.id">
                 <div class="view-images-list" v-if="item.fileType == 2">
								    <div class="delete-img" v-if="commonFlay" @click="deleteApplyWife(index, detailInfo.quotaFileList)"><i class="el-icon-error"></i></div>
                    <img v-if="item.fileType == 2" style="width:150px; height:150px;margin-right:10px" :src="item.filePath" alt="" @click="showApplyImageWife(item)">
                </div>
                 </div>
                 </div>  
                     <el-upload
                      v-if="commonFlay"
                        :action="uploadImgUrl"
                        list-type="picture-card"
                        :on-preview="handlePictureCardPreview"
                        :file-list="quotaFileList"
                        value-key="img_url"
                        :auto-upload="true"
                        :show-file-list="false"
                        :on-success="handlSuccessApplyWife"
                        :headers="headers"
						>
							<i class="el-icon-plus"></i>
						</el-upload>
              </div>
          </el-descriptions-item>
      </el-descriptions> -->

      <!-- <el-descriptions title="流水信息" border direction="vertical">
          <el-descriptions-item label="申请人近12个月银行流水记录" :span="3">
              <div class="images" style="display:flex">
                <div class="view-images">
                  <div v-for="(item,index) in detailInfo.quotaFileList" :key="item.id">
                    <div class="view-images-list" v-if="item.fileType == 3">
                      <div class="delete-img" v-if="commonFlay" @click="deleteFlow(index, detailInfo.quotaFileList)"><i class="el-icon-error"></i></div>
                      <img v-if="item.fileType == 3" style="width:150px; height:150px;margin-right:10px" :src="item.filePath" alt="" @click="showApplyFlow(item)">
                </div>
                  </div>
                 </div>  
                     <el-upload
                        v-if="commonFlay"
                        :action="uploadImgUrl"
                        list-type="picture-card"
                        :on-preview="handlePictureCardPreview"
                        :file-list="quotaFileList"
                        value-key="img_url"
                        :auto-upload="true"
                        :show-file-list="false"
                        :on-success="handlSuccessFlow"
                        :headers="headers"
						>
							<i class="el-icon-plus"></i>
						</el-upload>
              </div> 
          </el-descriptions-item>
      </el-descriptions> -->

      <div class="user_info">
       <el-descriptions title="经营信息" border :column="3" :contentStyle="CS">
            <el-descriptions-item label="经营场所地址">
              {{detailInfo.businessPremisesName}} {{detailInfo.address}}
            </el-descriptions-item>
            <!-- <el-descriptions-item label="养殖数量">
            {{detailInfo.numOfBreeding}}
            </el-descriptions-item> -->
            <el-descriptions-item label="牛数量（头）">{{detailInfo.cattleNum}}</el-descriptions-item>
            <el-descriptions-item label="羊数量（只）">{{detailInfo.sheepNum}}</el-descriptions-item>
            <!-- <el-descriptions-item label="其它活畜数量">{{detailInfo.otherNum}}</el-descriptions-item> -->
            <el-descriptions-item label="自有草场（亩）">{{detailInfo.ownPasture}}</el-descriptions-item>
            <el-descriptions-item label="租用草场（亩）">{{detailInfo.rentPasture}}</el-descriptions-item>
            <el-descriptions-item label="棚圈面积（㎡）">{{detailInfo.shedArea}}</el-descriptions-item>
            <el-descriptions-item label="房屋面积（㎡）">{{detailInfo.housesArea}}</el-descriptions-item>
            <!-- <el-descriptions-item label="棚圈（间）">{{detailInfo.shedNum}}</el-descriptions-item>
            <el-descriptions-item label="棚圈总价值（万）">{{detailInfo.shedAmtWan}}</el-descriptions-item>
            <el-descriptions-item label="房屋（平米）">{{detailInfo.housesArea}}</el-descriptions-item>
            <el-descriptions-item label="房屋价值（万）">{{detailInfo.housesAmtWan}}</el-descriptions-item>
            <el-descriptions-item label="生产机械（台）">{{detailInfo.prodMachinery}}</el-descriptions-item>
            <el-descriptions-item label="生产机械总价值（万）">{{detailInfo.prodMachineryAmtWan}}</el-descriptions-item>
            <el-descriptions-item label="其它财产信息">{{detailInfo.otherPropertyInfo}}</el-descriptions-item>
            <el-descriptions-item label="其它财产信息总价值 （万）">{{detailInfo.otherPropertyInfoAmtWan}}</el-descriptions-item> -->
          </el-descriptions>
          <div class="user_card">
            <span class="user_title">棚圈照片</span>
            <div class="images" style="display: flex">
              <div class="view-images">
                <div
                  v-for="(item) in shedPict"
                  :key="item.id"
                >
                  <div class="view-images-list">
                    <!-- <div
                      class="delete-img"
                      v-if="commonFlay"
                      @click="deleteAddress(index, shedPict)"
                    >
                      <i class="el-icon-error"></i>
                    </div> -->
                    <img
                     
                      style="width: 150px; height: 150px; margin-right: 10px"
                      :src="item"
                      alt=""
                      @click="showApplyAddress(item)"
                    />
                  </div>
                </div>
                <!-- Flow -->
              </div>
              <!-- <el-upload
                v-if="commonFlay"
                :action="uploadImgUrl"
                list-type="picture-card"
                :on-preview="handlePictureCardPreview"
                :file-list="quotaFileList"
                value-key="img_url"
                :auto-upload="true"
                :show-file-list="false"
                :on-success="handlSuccessAddress"
                :headers="headers"
              >
                <i class="el-icon-plus"></i>
              </el-upload> -->
            </div>
          </div>
          <div class="user_card">
            <span class="user_title">养殖活畜照片</span>
            <div class="images" style="display: flex">
              <div class="view-images">
                <div
                  v-for="(item, index) in livestockPic"
                  :key="item.id"
                >
                  <div class="view-images-list">
                    <div
                      class="delete-img"
                      v-if="commonFlay"
                      @click="deleteAnimal(index, livestockPic)"
                    >
                      <i class="el-icon-error"></i>
                    </div>
                    <img
                     
                      style="width: 150px; height: 150px; margin-right: 10px"
                      :src="item"
                      alt=""
                      @click="showApplyAnimal(item)"
                    />
                  </div>
                </div>
                <!-- Flow -->
              </div>
              <el-upload
                v-if="commonFlay"
                :action="uploadImgUrl"
                list-type="picture-card"
                :on-preview="handlePictureCardPreview"
                :file-list="quotaFileList"
                value-key="img_url"
                :auto-upload="true"
                :show-file-list="false"
                :on-success="handlSuccessAnimal"
                :headers="headers"
              >
                <i class="el-icon-plus"></i>
              </el-upload>
            </div>
          </div>
          <!--  申请人房屋照片-->
          <div class="user_card">
            <span class="user_title">申请人房屋照片</span>
            <div class="images" style="display:flex">
                <div class="view-images">
                  <div v-for="(item) in housesPic" :key="item.id">
                    <div class="view-images-list">
                      <img style="width:150px; height:150px;margin-right:10px" :src="item" alt="" @click="showApplyHouse(item)">
                    </div>
                  </div>
                 </div>  
              </div>
          </div>
          <div class="user_card">
            <span class="user_title">其它照片</span>
            <div class="images" style="display:flex">
                <div class="view-images">
                  <div v-for="(item) in otherPic" :key="item.id">
                    <div class="view-images-list">
                      <img style="width:150px; height:150px;margin-right:10px" :src="item" alt="" @click="showApplyOther(item)">
                    </div>
                  </div>
                 </div>  
              </div>
          </div>
        </div>
    <!--<div class="user_card">
            <span class="user_title">经营场所场地照片</span>
             <div class="images" style="display:flex">
                <div class="view-images">
                  <div v-for="(item,index) in detailInfo.quotaFileList" :key="item.id">
                    <div class="view-images-list" v-if="item.fileType == 4">
                      <div class="delete-img" v-if="commonFlay" @click="deleteAddress(index, detailInfo.quotaFileList)"><i class="el-icon-error"></i></div>
                      <img v-if="item.fileType == 4" style="width:150px; height:150px;margin-right:10px" :src="item.filePath" alt="" @click="showApplyAddress(item)">
                </div>
                  </div>
                 </div>  
                     <el-upload
                     v-if="commonFlay"
                        :action="uploadImgUrl"
                        list-type="picture-card"
                        :on-preview="handlePictureCardPreview"
                        :file-list="quotaFileList"
                        value-key="img_url"
                        :auto-upload="true"
                        :show-file-list="false"
                        :on-success="handlSuccessAddress"
                        :headers="headers"
						>
							<i class="el-icon-plus"></i>
						</el-upload>
              </div>
        </div>
        <div class="user_card">
            <span class="user_title">养殖活畜照片</span>
            <div class="images" style="display:flex">
                <div class="view-images">
                  <div v-for="(item,index) in detailInfo.quotaFileList" :key="item.id">
                    <div class="view-images-list" v-if="item.fileType == 5">
                      <div class="delete-img" v-if="commonFlay" @click="deleteAnimal(index, detailInfo.quotaFileList)"><i class="el-icon-error"></i></div>
                      <img v-if="item.fileType == 5" style="width:150px; height:150px;margin-right:10px" :src="item.filePath" alt="" @click="showApplyAnimal(item)">
                </div>
                  </div>
                 </div>  
                     <el-upload
                     v-if="commonFlay"
                        :action="uploadImgUrl"
                        list-type="picture-card"
                        :on-preview="handlePictureCardPreview"
                        :file-list="quotaFileList"
                        value-key="img_url"
                        :auto-upload="true"
                        :show-file-list="false"
                        :on-success="handlSuccessAnimal"
                        :headers="headers"
						>
							<i class="el-icon-plus"></i>
						</el-upload>
              </div>
        </div> -->
    </div>
    </div>

    <el-dialog
      title=""
      :visible.sync="dialogVisible"
    >
      <img style="width:100%" :src="dialogImageUrl" alt="">
    </el-dialog>
    <el-button v-if="detailInfo.approvalStatus == 3 && type == 0 && detailInfo.type == 1" type="primary" @click="changeType">去审核</el-button>
    </div>
</template>

<script>
import baseInfo from "../components/baseInfo/index.vue"
import { queryApplyDetail, changeApproStatus } from '@/api/nlbao/guarantee'
import {basicPath2} from '@/api/base.js'
import { getToken } from "@/utils/auth";
import axios from 'axios'

export default {
 components: {
    baseInfo
  },
  data() {
    return {
      isShow: false,
      showTitle: '点击查看申请详情',
      icon:'el-icon-arrow-down',
      ruleForm: {},
      rules: {
        approvalStatus: [
          { required: true, message: "请选择审核意见", trigger: "change" }
        ],
        // guarAmt: [
        //   { required: true, message: "请输入推荐企业评估额度", trigger: "blur" }
        // ]
      },
      uploadImgUrl: process.env.VUE_APP_BASE_API + `${basicPath2}files/obs/fileUpload`, // 上传的图片服务器地址
       headers: {
        Authorization: getToken(),
      },
      orderId: "",
      // active: undefined,
      active: undefined,
      orderInfo: {},
      orderData: [],
      id: '',
      detailInfo:{},
      dialogVisible: false,
      dialogImageUrl: '',
      hideUpload: true,
      // 法人身份证信息
      faImageList:[],
      envflag: false,
      maxCount: 2, //证件
      // 配偶身份证信息
      wifeList:[],
      wifeFlag: false,
      wifeCount: 2,
      // 申请人征信
      quotaFileList:[],
      commonFlay: false,
      imgUrl:'',
      disabled: false,
      addressOptions: [],
      province:'',
      city: '',
      area: '',
      detailAdd: '',
      addressDetail:'',
      aniNumber:'',
      type: "",
       CS: {
        width: "200px", //最小宽度
        "word-break": "break-all", //过长时自动换行
      },
      C_S: {
        width: "280px", //最小宽度
        "word-break": "break-all" //过长时自动换行
      },
      shedPic: [],
      livestockPic: [],
      housesPic: [],
      otherPic: [],
      extsysUserInfo: {}
    }
  },
   filters: {            
    // 状态 (0取消 1提交成功，2畜牧师成功，3运营人成功，4推荐企业成功，5银行成功)
    formartSex: (type) =>{
      const statusMap = {
        1: '未婚',
        2: '已婚',
        3: '离异',
        4: '丧偶',
      }
      return statusMap[type]
    }
  },
  created() {
    this.extsysUserInfo = JSON.parse(window.localStorage.getItem('extsysUserInfo'))
    this.getAddressList()
    this.id = this.$route.query.id;
    this.type = this.$route.query.type
    this.type == 0 ?  this.isShow = true:this.isShow = false;
    console.log(this.id)
    this.getQueryApplyDetail();
  },
  mounted() {},
  methods: {
     // 获取地址信息
   async getAddressList(){
     const {data : res} = await axios.get('/city.json');
     this.addressOptions = res
    },
    async getQueryApplyDetail(){
     const {data: res} = await queryApplyDetail({quotaId: this.id});
     console.log(res);
     this.detailInfo = res;
     this.active = res.approvalStatus
     let addressArr = res.businessPremises.split(',');
    this.shedPic = this.detailInfo.shedPic && this.detailInfo.shedPic.split(',');
    this.livestockPic = this.detailInfo.livestockPic && this.detailInfo.livestockPic.split(',');
    this.housesPic = this.detailInfo.housesPic && this.detailInfo.housesPic.split(',');
    this.otherPic = this.detailInfo.otherPic && this.detailInfo.otherPic.split(',');
    if (this.detailInfo.userCertPic1) {
      this.faImageList.push({
        url: this.detailInfo.userCertPic1,
        type:1
      })
    }
    if (this.detailInfo.userCertPic2) {
      this.faImageList.push({
        url: this.detailInfo.userCertPic2,
        type:2
      })
    }
    /*  this.wifeList.push({
       url: this.detailInfo.spouseIdPic1,
     })
     this.wifeList.push({
       url: this.detailInfo.spouseIdPic2,
     }) */
      if(this.detailInfo.spouseIdPic1) {
        this.wifeList.push({
        url: this.detailInfo.spouseIdPic1
      })
      }
      if(this.detailInfo.spouseIdPic2) {
         this.wifeList.push({
        url: this.detailInfo.spouseIdPic2
      })
      }

  },

    changeType() {
      this.type = 1
      this.type == 0 ? (this.isShow = true) : (this.isShow = false)
    },
  // 申请人及配偶身份证上传
    deleteimg(index, fileList){      
      this.$nextTick(() =>{
        fileList.splice(index, 1);
        this.faImageList = fileList;
        this.envflag = !(fileList.length >= this.maxCount);
      })
		},
    showimage(file){
      console.log(file)
			this.dialogImageUrl = file.url
			this.dialogVisible = true
		},
    handlEnvironmentSuccess(res, file, fileList){
      this.faImageList.push({
        url: res.result[0].objectUrl
      });
      this.$nextTick(() => {
        this.envflag = !(fileList.length >= this.maxCount);
      })
    },
    deleteWifeImg(index, fileList){ 
      fileList.splice(index, 1);
        this.wifeList = fileList;  
        this.$nextTick(() =>{
         this.wifeFlag = !(fileList.length >= this.maxCount);
      })
		},
    showWifeImage(file){
			this.dialogImageUrl = file.url
			this.dialogVisible = true
		},
    handlWifeSuccess(res, file, fileList){
      this.wifeList.push({
        url: res.result[0].objectUrl
      });
      this.$nextTick(() => {
        this.wifeFlag = !(fileList.length >= this.maxCount);
      })
    },
    handlePictureCardPreview(){},
    // 申请人征信上传
     deleteApply(index, fileList){ 
       console.log(index)
       console.log(fileList)
       fileList.splice(index, 1);
       this.detailInfo.quotaFileList = fileList;  
		},
    showApplyImage(file){
      console.log(file)
			this.dialogImageUrl = file.filePath
			this.dialogVisible = true
		},
    handlSuccessApply(res, file, fileList){
      this.detailInfo.quotaFileList.push({
        fileType: 1,
        fileName: file.url,
        filePath: res.result[0].objectUrl
      });
    },
    // 配偶征信信息
    deleteApplyWife(index, fileList){ 
       console.log(index)
       console.log(fileList)
       fileList.splice(index, 1);
       this.detailInfo.quotaFileList = fileList;  
		},
    showApplyImageWife(file){
      console.log(file)
			this.dialogImageUrl = file.filePath
			this.dialogVisible = true
		},
    handlSuccessApplyWife(res, file, fileList){
      this.detailInfo.quotaFileList.push({
        fileType: 2,
        fileName: file.url,
        filePath: res.result[0].objectUrl
      });
    },
    // 申请人近12个月银行流水记录    
    deleteFlow(index, fileList){ 
       console.log(index)
       console.log(fileList)
       fileList.splice(index, 1);
       this.detailInfo.quotaFileList = fileList;  
		},
    showApplyFlow(file){
      console.log(file)
			this.dialogImageUrl = file.filePath
			this.dialogVisible = true
		},
    handlSuccessFlow(res, file, fileList){
      this.detailInfo.quotaFileList.push({
        fileType: 3,
        fileName: file.url,
        filePath: res.result[0].objectUrl
      });
    },
    // 经营场所场地照片
        deleteAddress(index, fileList){ 
       console.log(index)
       console.log(fileList)
       fileList.splice(index, 1);
       this.detailInfo.quotaFileList = fileList;  
		},
    showApplyAddress(file){
      console.log(file)
			this.dialogImageUrl = file
			this.dialogVisible = true
		},
    handlSuccessAddress(res, file, fileList){
      this.detailInfo.quotaFileList.push({
        fileType: 4,
        fileName: file.url,
        filePath: res.result[0].objectUrl
      });
    },
    // 养殖活畜照片
      deleteAnimal(index, fileList){ 
       console.log(index)
       console.log(fileList)
       fileList.splice(index, 1);
       this.detailInfo.quotaFileList = fileList;  
		},
    showApplyAnimal(file){
      console.log(file)
			this.dialogImageUrl = file
			this.dialogVisible = true
		},
    showApplyHouse(file){
			this.dialogImageUrl = file
			this.dialogVisible = true
		},
    showApplyOther(file){
      console.log(file)
			this.dialogImageUrl = file
			this.dialogVisible = true
		},
    handlSuccessAnimal(res, file, fileList){
      this.detailInfo.quotaFileList.push({
        fileType: 5,
        fileName: file.url,
        filePath: res.result[0].objectUrl
      });
    },
    cancel(){
        this.commonFlay = false
        this.disabled = false;
        this.envflag = false;
        this.wifeFlag = false
    },
    //审核
    handelSubmitForm(){
      this.$refs.ruleForm.validate((valid) => {
          if(valid) {
            if(this.ruleForm.approvalStatus === 6) {
              const params = {
                id: this.id,
                approvalStatus: 6,
                reason: this.ruleForm.reason,
                companyUserId: this.extsysUserInfo.access_token
              }
               if(!this.ruleForm.reason) {
                    this.$message.error("审核意见不能为空")
                  } else {
                    changeApproStatus(params).then(res => {
                    console.log(res.stautscode);
                    if(res.stautscode == 200) {
                      this.$message.success('驳回成功')
                      this.commonFlay = false
                      this.disabled = false;
                      this.envflag = false;
                      this.wifeFlag = false
                      this.$router.go(-1)
                    } else {
                      this.$message.error(res.msg)
                    }
                   })
                  }
            } else {
              const params = {
                id: this.id,
                approvalStatus: 4,
                guarAmt: this.ruleForm.guarAmt,
                companyUserId: this.extsysUserInfo.access_token
              }
              changeApproStatus(params).then(res => {
                  console.log(res);
                  if(res.stautscode == 200) {
                      this.$message.success('审核成功')
                      this.commonFlay = false
                      this.disabled = false;
                      this.envflag = false;
                      this.wifeFlag = false
                      this.$router.go(-1)
                    } else {
                      this.$message.error(res.msg)
                    }
              })
            }
          }
      })
    },
    // 编辑
  /*  handelSubmit(){
     const addressId = this.marketAddressid.join(',');
     console.log(this.faImageList)
     console.log(this.wifeList)
     const params = {
       id: this.id,
       userIdPic1: this.faImageList?.[0]?.url || '',
       userIdPic2: this.faImageList?.[1]?.url || '',
       spouseIdPic1: this.wifeList?.[0]?.url || '',
       spouseIdPic2: this.wifeList?.[1]?.url || '',
       quotaFileList: this.detailInfo.quotaFileList,
       businessPremises: addressId,
       address: this.addressDetail,
       numOfBreeding: this.aniNumber
     }
    changeApproStatus(params).then(res => {
      console.log(res.stautscode);
      if(res.stautscode == 200) {
        this.$message.success('操作成功')
        this.commonFlay = false
        this.disabled = false;
        this.envflag = false;
        this.wifeFlag = false
      } else {
        this.$message.error(res.msg)
      }
      })
   }, */
    handelShow() {
        this.isShow = !this.isShow;
        this.isShow == true ? this.showTitle = '收起申请详情' :  this.showTitle = '展开查看申请详情'
        this.isShow == true ? this.icon = 'el-icon-arrow-up' :  this.icon = 'el-icon-arrow-down'
    },

  // 启用/禁用编辑
   handelEdit(){
     this.disabled = true
		 this.hideUpload = false
     this.commonFlay = true
   },
   
   // 三级地址选择
     handleChange(value) {
        console.log(value);
        if (value && value.length != 0) {
          let arr = this.$refs['cascaderAddr'].getCheckedNodes()[0].pathLabels;
          console.log(arr);
          this.detailAdd = arr.join(',')
          this.province = arr[0];
          this.city = arr[1];
          this.area = arr[2];
        }
    },

  }
}
</script>

<style scoped lang="scss">
.is_show{
    width: 100%;
    text-align: center;
    color: rgb(22, 155, 213);
    cursor: pointer;
}
::v-deep .el-steps--horizontal {
  width: 80%;
}
.step_style {
  width: 100%;
  display: flex;
  justify-content: center;
  margin: 10px 0;
}
::v-deep .el-descriptions__header {
  background-color: rgb(242, 242, 242);
  padding: 0 20px;
  margin: 30px 0 10px 0;
  height: 40px;
}

.user_info{
    .user_card{
        .user_title{
            margin: 10px 0;
            display: inline-block;
            color: rgb(153, 153, 153);
            font-size: 14px;
        }
        .images{
           display: flex;
            .el-image {
                width: 150px;
                height: 150px;
                margin-right: 20px;
            }
        }
    }
}
.step_style {
  width: 100%;
  margin: 10px 0;
}
.view-images{
	display: flex;
  flex-wrap: wrap;
	.view-images-list {
		width: 146px;
		height: 146px;
		display: inline-block;
		margin: 0 10px 0 10px;
		position: relative;
		.delete-img {
			display: inline-block;
			font-size: 24px;
			cursor: pointer;
			color: rgba(0,0,0,0.5);
			position: absolute;
		top: -16px;
    right: -9px;
		}
	img{
		width: 100%;
		height: 100%;
		border-radius: 10px;
		cursor: pointer;
	}
}
}
</style>
