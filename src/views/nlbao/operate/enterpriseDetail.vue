<template>
  <div class="app-container">
    <div class="table">
      <div class="step_style" v-if="type == 0">
        <el-alert
          v-if="detailInfo.approvalStatus == 6"
          :title="detailInfo.approvalStatusName"
          type="warning"
          :description="detailInfo.reason"
          :closable="false"
        >
        </el-alert>
        <el-alert
          v-else
          :title="detailInfo.approvalStatusName"
          type="warning"
          :closable="false"
        >
        </el-alert>
      </div>

      <!-- 放款信息 -->
      <el-descriptions title="放款信息" border :contentStyle="C_S">
        <el-descriptions-item label="放款单号">
          <span v-if="detailInfo.approvalStatus == 7">{{
            detailInfo.quotaCode
          }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="受理银行">{{
          detailInfo.bankName
        }}</el-descriptions-item>
        <el-descriptions-item label="放款金额（元）">{{
          detailInfo.actualAmtYuan
        }}</el-descriptions-item>
        <el-descriptions-item label="放款期限（月）">{{
          detailInfo.bankTerm
        }}</el-descriptions-item>
        <el-descriptions-item label="利率（年化）">{{
          detailInfo.quotaRate
        }}</el-descriptions-item>
        <el-descriptions-item label="日期范围"
          >{{ detailInfo.quotaStartTime }} ～
          {{ detailInfo.quotaEndTime }}</el-descriptions-item
        >
        <!-- <el-descriptions-item label="银行地址">{{
          detailInfo.bankAddress
        }}</el-descriptions-item>
        <el-descriptions-item label="银行电话">{{
          detailInfo.bankContactPhone
        }}</el-descriptions-item>
        <el-descriptions-item label="信贷员">{{detailInfo.bankOfficerName}}</el-descriptions-item> -->
      </el-descriptions>
      <div class="user_info" v-if="detailInfo.userType == 1">
        <el-descriptions title="企业信息" border :contentStyle="C_S">
          <el-descriptions-item label="申请人">{{
            detailInfo.userName
          }}</el-descriptions-item>
          <el-descriptions-item label="额度类型">
             <dict-tag :options="dict.type.nyb_loan_type" :value="detailInfo.type" />
          </el-descriptions-item>
          <el-descriptions-item label="资金用途">{{
            detailInfo.quotaPurpose
          }}</el-descriptions-item>
          <el-descriptions-item label="推荐企业">{{
            detailInfo.companyName
          }}</el-descriptions-item>
          <el-descriptions-item label="企业证件号码">{{
            detailInfo.userIdNo
          }}</el-descriptions-item>
        </el-descriptions>
        <div class="user_card">
          <span class="user_title">企业证件照片</span>
          <div class="images">
            <div class="view-images">
               <el-image
              v-if="detailInfo.userIdPic3"
                :src="detailInfo.userIdPic3"
                @click="handelPreview(detailInfo.userIdPic3)"
              ></el-image>
            </div>
          </div>
        </div>
      </div>

      <div class="user_info" v-if="detailInfo.userType == 1">
        <el-descriptions title="法人信息" border :contentStyle="C_S">
          <el-descriptions-item label="法人姓名">{{
            detailInfo.userEmerName
          }}</el-descriptions-item>
          <el-descriptions-item label="法人身份证号">{{
            detailInfo.userEmerIdNo
          }}</el-descriptions-item>
          <el-descriptions-item label="法人手机号">{{
            detailInfo.userEmerPhone
          }}</el-descriptions-item>
        </el-descriptions>
        <div class="user_card">
          <span class="user_title">法人身份证信息</span>
          <div class="images">
            <div class="view-images">
              <el-image
              v-if="detailInfo.userIdPic1"
                :src="detailInfo.userIdPic1"
                @click="handelPreview(detailInfo.userIdPic1)"
              ></el-image>
              <el-image
                v-if="detailInfo.userIdPic2"
                :src="detailInfo.userIdPic2"
                @click="handelPreview(detailInfo.userIdPic2)"
              ></el-image>
            </div>
          </div>
        </div>
      </div>
      <div class="user_info" v-if="detailInfo.userType == 3">
        <el-descriptions title="用户信息" border :contentStyle="C_S">
          <el-descriptions-item label="用户姓名">{{
            detailInfo.userEmerName
          }}</el-descriptions-item>
          <el-descriptions-item label="用户身份证号">{{
            detailInfo.userEmerIdNo
          }}</el-descriptions-item>
          <el-descriptions-item label="用户手机号">{{
            detailInfo.userEmerPhone
          }}</el-descriptions-item>
          <el-descriptions-item label="额度类型">
             <dict-tag :options="dict.type.nyb_loan_type" :value="detailInfo.type" />
          </el-descriptions-item>
          <el-descriptions-item label="申请方式">{{
            detailInfo.applyTypeName
          }}</el-descriptions-item>
          <el-descriptions-item label="推荐企业">{{
            detailInfo.companyName
          }}</el-descriptions-item>
          <el-descriptions-item label="资金用途">{{
            detailInfo.quotaPurpose
          }}</el-descriptions-item>
        </el-descriptions>
        <!-- <div class="user_card">
          <span class="user_title">用户身份证信息</span>
          <div class="images">
            <div class="view-images">
              <el-image
              v-if="detailInfo.userIdPic1"
                :src="detailInfo.userIdPic1"
                @click="handelPreview(detailInfo.userIdPic1)"
              ></el-image>
              <el-image
                v-if="detailInfo.userIdPic2"
                :src="detailInfo.userIdPic2"
                @click="handelPreview(detailInfo.userIdPic2)"
              ></el-image>
            </div>
          </div>
        </div> -->
      </div>
    </div>
    <el-dialog :visible.sync="dialogVisible">
              <img width="100%" :src="dialogImageUrl" alt="" />
            </el-dialog>
  </div>
</template>

<script>
import baseInfo from "../components/baseInfo/index.vue";
import { queryApplyDetail } from "@/api/nlbao/guarantee";

export default {
  components: {
    baseInfo,
  },
  dicts: ["nyb_loan_type"],
  data() {
    return {
      id: null,
      detailInfo: {},
      type: "",
      CS: {
        width: "380px", //最小宽度
        "word-break": "break-all", //过长时自动换行
      },
      C_S: {
        width: "280px", //最小宽度
        "word-break": "break-all", //过长时自动换行
      },
      dialogVisible: false,
      dialogImageUrl:''
    };
  },
  filters: {},
  created() {
    this.id = this.$route.query.id;
    this.getQueryApplyDetail();
  },
  mounted() {},
  methods: {
    async getQueryApplyDetail() {
      const { data: res } = await queryApplyDetail({ quotaId: Number(this.id) });
      console.log(res);
      this.detailInfo = res;
    },
    handelPreview(imgUrl) {
      this.dialogImageUrl = imgUrl;
      this.dialogVisible = true;
    },
  },
};
</script>

<style scoped lang="scss">
.is_show {
  width: 100%;
  text-align: center;
  color: rgb(22, 155, 213);
  cursor: pointer;
}
::v-deep .el-steps--horizontal {
  width: 80%;
}
.step_style {
  width: 100%;
  margin: 10px 0;
}
::v-deep .el-descriptions__header {
  background-color: rgb(242, 242, 242);
  padding: 0 20px;
  margin: 30px 0 10px 0;
  height: 40px;
}

.user_info {
  .user_card {
    .user_title {
      margin: 10px 0;
      display: inline-block;
      color: rgb(153, 153, 153);
      font-size: 14px;
    }
    .images {
      display: flex;
      flex-wrap: wrap;
      .el-image {
        width: 150px;
        height: 150px;
        margin-right: 20px;
      }
    }
  }
}

.view-images {
  display: flex;
  flex-wrap: wrap;
  .view-images-list {
    width: 146px;
    height: 146px;
    display: inline-block;
    margin: 0 10px 0 10px;
    position: relative;
    .delete-img {
      display: inline-block;
      font-size: 24px;
      cursor: pointer;
      color: rgba(0, 0, 0, 0.5);
      position: absolute;
      top: -16px;
      right: -9px;
    }
    img {
      width: 100%;
      height: 100%;
      border-radius: 10px;
      cursor: pointer;
    }
  }
}
</style>
