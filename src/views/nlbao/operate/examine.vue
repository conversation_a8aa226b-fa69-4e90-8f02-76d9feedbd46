<template>
  <div class="app-container">
    <div class="table">
       <div class="step_style" v-if="type==0">
       <el-alert
         v-if="detailInfo.approvalStatus == 6"
        :title="detailInfo.approvalStatusName"
        type="warning"
        :description="detailInfo.reason"
        :closable="false">
      </el-alert>
       <el-alert
        v-else
        :title="detailInfo.approvalStatusName"
        type="warning"
        :closable="false">
      </el-alert>
      </div>
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-width="150px"
        class="demo-ruleForm"
        v-if="type == 1"
      >
        <el-form-item label="申请人申请额度">
          <div>{{ detailInfo.applyAmt }} 元
            <el-tooltip effect="dark" placement="top">
              <div slot="content" v-html="tooltips"></div>
              <img style="width: 16px;height: 16px; margin-left: 10px;" src="../../../assets/images/mes/info-circle.png" />
            </el-tooltip>
          </div>
        </el-form-item>

        <el-form-item label="审核意见" prop="approvalStatus">
          <el-radio-group
            v-model="ruleForm.approvalStatus"
            @change="handleChangeApprovalStatus"
          >
            <el-radio :label="3">审核通过</el-radio>
            <el-radio :label="6">审核驳回</el-radio>
            <el-radio :label="11">审核驳回并添加申请人至黑名单</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label=""
          prop="reason"
          v-if="ruleForm.approvalStatus == 6 || ruleForm.approvalStatus == 11 "
        >
          <el-select v-model="ruleForm.reason" placeholder="请选择">
            <el-option
              v-for="item in checkOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label=""
          prop="reason"
          v-if="ruleForm.approvalStatus == 6 || ruleForm.approvalStatus == 11 "
        >
          <el-input
            type="textarea"
            v-model="reasonOther"
            style="width: 50%"
            placeholder="请输入"
            v-if='ruleForm.reason == "其他"'
          ></el-input>
        </el-form-item>
        <div v-if="ruleForm.approvalStatus == 3">
          <el-form-item label="放款银行" prop="bankUserId">
            <el-select
              v-model="ruleForm.bankUserId"
              remote
              filterable
              reserve-keyword
              placeholder="请输入银行名称"
              :remote-method="remoteMethod"
            >
              <el-option
                v-for="item in bankOptions"
                :key="item.value"
                :label="item.text"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="平台评估额度" prop="platformAmt">
            <el-input
              v-model="ruleForm.platformAmt"
              type='number'
              style="width: 50%"
              oninput="if(value.length > 8) value=value.slice(0, 8)"
              placeholder="平台评估额度"
            ></el-input>
            元
          </el-form-item>
        </div>
        <el-form-item>
          <el-button type="primary" @click="handelSubmitForm">提交</el-button>
        </el-form-item>
      </el-form>
      <div class="is_show" @click="handelShow" v-if="type == 1">
        {{ showTitle }}
        <i :class="icon"></i>
      </div>
      <div style="display: none">
        <el-button @click="handelEdit" v-if="!disabled">编辑</el-button>
        <el-button type="primary" @click="handelSubmit" v-if="disabled"
          >保存</el-button
        >
        <el-button @click="cancel" v-if="disabled">取消</el-button>
      </div>

      <div v-show="isShow">
        <!-- 放款信息 -->
        <el-descriptions title="放款信息" border v-if="type==0" :contentStyle="C_S">
        <!-- <el-descriptions-item label="放款单号">
          <span v-if="detailInfo.approvalStatus==7">{{detailInfo.quotaCode}}</span>
        </el-descriptions-item> -->
        <el-descriptions-item label="受理银行">{{detailInfo.bankName}}</el-descriptions-item>
        <el-descriptions-item label="放款金额（元）">{{detailInfo.actualAmt}}</el-descriptions-item>
        <el-descriptions-item label="放款期限（月）">{{detailInfo.bankTerm}}</el-descriptions-item>
        <el-descriptions-item label="利率（年化）">{{detailInfo.quotaRate}}</el-descriptions-item>
        <el-descriptions-item label="日期范围">{{detailInfo.quotaStartTime}} ～ {{detailInfo.quotaEndTime}}</el-descriptions-item>
        <!-- <el-descriptions-item label="银行地址">{{detailInfo.bankAddress}}</el-descriptions-item>
        <el-descriptions-item label="银行电话">{{detailInfo.bankContactPhone}}</el-descriptions-item>
        <el-descriptions-item label="信贷员">信贷员</el-descriptions-item> -->
      </el-descriptions>
        <template>
        <!-- <el-descriptions title="银行评估" border v-if="type==0" :contentStyle="CS">
        <el-descriptions-item label="银行评估额度（元）">{{detailInfo.bankAmt}}</el-descriptions-item>
      </el-descriptions> -->
      <!-- <el-descriptions title="服务店铺评估" border v-if="type==0" :contentStyle="CS">
        <el-descriptions-item label="服务店铺评估额度（元）">{{detailInfo.guarAmt}}</el-descriptions-item>
      </el-descriptions> -->
      </template>
      <!-- 。。。。。。。 -->
      <el-descriptions title="平台评估" border v-if="type==0" :contentStyle="CS">
        <el-descriptions-item label="平台评估额度（元）">{{detailInfo.platformAmt}}</el-descriptions-item>
        <el-descriptions-item label="分配银行">{{detailInfo.bankName}}</el-descriptions-item>
      </el-descriptions>
        <el-descriptions title="服务店铺信息" border v-if="detailInfo.guarId">
          <el-descriptions-item label="服务店铺名称">{{
            detailInfo.companyName
          }}</el-descriptions-item>
          <el-descriptions-item label="法人">{{
            detailInfo.corprateName
          }}</el-descriptions-item>
          <el-descriptions-item label="企业代码">{{
            detailInfo.certNo
          }}</el-descriptions-item>
        </el-descriptions>

        <el-descriptions title="申请信息" border>
          <el-descriptions-item label="申请人姓名">{{
            detailInfo.userName
          }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{
            detailInfo.userPhone
          }}</el-descriptions-item>
          <el-descriptions-item label="身份证号">{{
            detailInfo.userIdNo
          }}</el-descriptions-item>
          <el-descriptions-item label="申请额度（元）">{{
            detailInfo.applyAmt
          }}
          <el-tooltip effect="dark" placement="top">
            <div slot="content" v-html="tooltips"></div>
            <img style="width: 16px;height: 16px; margin-left: 10px;" src="../../../assets/images/mes/info-circle.png" />
          </el-tooltip>
          </el-descriptions-item>
          <!-- <el-descriptions-item label="额度类型">
            <el-select v-model="detailInfo.type" @change="changType" placeholder="请选择额度类型" v-if='type != 0'>
              <el-option label="专项额度" :value="1"> </el-option>
              <el-option label="通用额度" :value="2"> </el-option>
            </el-select>
            <span v-else>{{ detailInfo.typeName }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="申请方式" v-if="detailInfo.type == 2">
            <el-select v-model="detailInfo.applyType" placeholder="请选择申请方式" v-if='type != 0'>
              <el-option label="活畜抵押" :value="1"> </el-option>
              <el-option label="信用额度" :value="2"> </el-option>
            </el-select>
            <span v-else>{{ detailInfo.applyTypeName }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="推荐企业" v-if="detailInfo.type == 1">
            <el-select v-model="detailInfo.guarId" placeholder="请选择推荐企业" v-if='type != 0'>
              <el-option v-for="(item, index) in guarantorList" :key="index" :label="item.companyName" :value="item.id"> </el-option>
            </el-select>
            <span v-else>{{ detailInfo.applyTypeName }}</span>
          </el-descriptions-item> -->
          <el-descriptions-item label="申请期限（月）">
            {{detailInfo.quotaTerm}}</el-descriptions-item>
          <el-descriptions-item label="资金用途">{{
            detailInfo.quotaPurpose
          }}</el-descriptions-item>
          <el-descriptions-item label="申请时间">{{
            detailInfo.createTime
          }}</el-descriptions-item>
          <!-- <el-descriptions-item label="借款金额（元）">{{
            detailInfo.debtAmt
          }}</el-descriptions-item>
          <el-descriptions-item label="借款银行">{{
            detailInfo.debtExplain
          }}</el-descriptions-item> -->
        </el-descriptions>
        <el-descriptions title="订单信息" border v-if="detailInfo.orderInfo">
          <el-descriptions-item label="收款方全称">{{
            detailInfo.companyName
          }}</el-descriptions-item>
          <el-descriptions-item label="订单编号">{{
            detailInfo.userPhone
          }}</el-descriptions-item>
          <el-descriptions-item label="支付方式">预付款 + 尾款</el-descriptions-item>
          <el-descriptions-item label="订单金额（元）">{{
            detailInfo.applyAmt
          }}</el-descriptions-item>
          <el-descriptions-item label="下单时间">{{
            detailInfo.createTime
          }}</el-descriptions-item>
        </el-descriptions>
      <el-table :data="dataOrderInfoList" v-if="detailInfo.orderInfo" highlight-current-row >
        <el-table-column align="center" label="商品名称" prop="goodsName" fixed />
        <el-table-column align="center" label="单价" prop="singlePrice" fixed />
        <el-table-column align="center" label="数量" prop="goodsNum" fixed />
      </el-table>

        <el-descriptions title="资产状况" border v-if="detailInfo.debtAmt">
          <el-descriptions-item label="负债总金额（元）">{{
            detailInfo.debtAmt
          }}</el-descriptions-item>
            <el-descriptions-item v-for='(item, index) in debtExplain' :key="index">
              <template slot="label">
                借款银行：{{
              item.name
            }}
              </template> 借款金额（万）：{{
              item.amt
            }}</el-descriptions-item>
            <!-- <el-descriptions-item label="借款金额（元）">{{
              item.amt
            }}</el-descriptions-item> -->
        </el-descriptions>
        <div class="user_info">
          <el-descriptions title="身份信息" border>
            <el-descriptions-item label="婚姻状况" v-if="detailInfo.maritalStatus">{{
              detailInfo.maritalStatus | formartSex
            }}</el-descriptions-item>
            <el-descriptions-item v-if="detailInfo.maritalStatus == 2" label="配偶姓名">{{
              detailInfo.spouseName
            }}</el-descriptions-item>
            <el-descriptions-item v-if="detailInfo.maritalStatus == 2" label="配偶身份证号">{{
              detailInfo.spouseIdNo
            }}</el-descriptions-item>
            <el-descriptions-item v-if="detailInfo.maritalStatus != 1" label="是否有子女">{{
              detailInfo.childFlag == 1 ? '是' : '否'
            }}</el-descriptions-item>
            <el-descriptions-item v-if="detailInfo.maritalStatus != 1" label="子女情况">{{
              handelChildSituation(childSituation, detailInfo.childInfo)
            }}</el-descriptions-item>
          </el-descriptions>

          <div class="user_card" v-if="faImageList.length > 0">
            <span class="user_title">申请方证件信息</span>
            <div class="images">
              <div class="view-images">
                <div
                  class="view-images-list"
                  v-for="(imgItem, index) in faImageList"
                  :key="index"
                >
                <image-preview :src="imgItem.url" ></image-preview>
                  <!-- <img :src="imgItem.url" alt="" @click="showimage(imgItem)" /> -->
                </div>
              </div>
            </div>
          </div>
          <div class="user_card" v-if="detailInfo.maritalStatus == 2">
            <span class="user_title">配偶身份证信息</span>
            <!-- <div class="images">
               <el-image style="width:150px; height:150px" :src="detailInfo.spouseIdPic1"></el-image>
                <el-image style="width:150px; height:150px" :src="detailInfo.spouseIdPic2"></el-image>
            </div> -->
            <div class="images">
              <div class="view-images">
                <div
                  class="view-images-list"
                  v-for="(imgItem, index) in wifeList"
                  :key="index"
                >
                  <!-- <div
                    class="delete-img"
                    v-if="type != 0"
                    @click="deleteWifeImg(index, wifeList)"
                  >
                    <i class="el-icon-error"></i>
                  </div> -->
                  <!-- <img
                    :src="imgItem.url"
                    alt=" "
                    @click="showWifeImage(imgItem)"
                  /> -->
                  <image-preview :src="imgItem.url" ></image-preview>
                </div>
                <!-- <el-upload
                  v-if="type != 0 && wifeList.length < 2"
                  :action="uploadImgUrl"
                  list-type="picture-card"
                  :on-preview="handlePictureCardPreview"
                  :file-list="wifeList"
                  value-key="img_url"
                  :limit="2"
                  :auto-upload="true"
                  :show-file-list="false"
                  :on-success="handlWifeSuccess"
                  :headers="headers"
                >
                  <i class="el-icon-plus"></i>
                </el-upload> -->
              </div>
            </div>
          </div>
        </div>

        <!-- <el-descriptions title="征信信息" border direction="vertical">
          <el-descriptions-item label="申请人征信信息" :span="3">
            <div class="images" style="display: flex">
              <div class="view-images">
                <div
                  v-for="(item, index) in detailInfo.quotaFileList"
                  :key="item.id"
                >
                  <div class="view-images-list" v-if="item.fileType == 1">
                    <div
                      class="delete-img"
                     v-if="type != 0"
                      @click="deleteApply(index, detailInfo.quotaFileList)"
                    >
                      <i class="el-icon-error"></i>
                    </div>
                    <img
                      v-if="item.fileType == 1"
                      style="width: 150px; height: 150px; margin-right: 10px"
                      :src="item.filePath"
                      alt=""
                      @click="showApplyImage(item)"
                    />
                  </div>
                </div>
              </div>
              <el-upload
               v-if="type != 0"
                :action="uploadImgUrl"
                list-type="picture-card"
                :on-preview="handlePictureCardPreview"
                :file-list="quotaFileList"
                value-key="img_url"
                :auto-upload="true"
                :show-file-list="false"
                :on-success="handlSuccessApply"
                :headers="headers"
              >
                <i class="el-icon-plus"></i>
              </el-upload>
            </div>
            <el-dialog :visible.sync="dialogVisible">
              <img width="100%" :src="dialogImageUrl" alt="" />
            </el-dialog>
          </el-descriptions-item>
          <el-descriptions-item label="配偶征信信息" :span="3">
            <div class="images" style="display: flex">
              <div class="view-images">
                <div
                  v-for="(item, index) in detailInfo.quotaFileList"
                  :key="item.id"
                >
                  <div class="view-images-list" v-if="item.fileType == 2">
                    <div
                      class="delete-img"
                     v-if="type != 0"
                      @click="deleteApplyWife(index, detailInfo.quotaFileList)"
                    >
                      <i class="el-icon-error"></i>
                    </div>
                    <img
                      v-if="item.fileType == 2"
                      style="width: 150px; height: 150px; margin-right: 10px"
                      :src="item.filePath"
                      alt=""
                      @click="showApplyImageWife(item)"
                    />
                  </div>
                </div>
              </div>
              <el-upload
               v-if="type != 0"
                :action="uploadImgUrl"
                list-type="picture-card"
                :on-preview="handlePictureCardPreview"
                :file-list="quotaFileList"
                value-key="img_url"
                :auto-upload="true"
                :show-file-list="false"
                :on-success="handlSuccessApplyWife"
                :headers="headers"
              >
                <i class="el-icon-plus"></i>
              </el-upload>
            </div>
          </el-descriptions-item>
        </el-descriptions>

        <el-descriptions title="流水信息" border direction="vertical">
          <el-descriptions-item label="申请人近12个月银行流水记录" :span="3">
            <div class="images" style="display: flex">
              <div class="view-images">
                <div
                  v-for="(item, index) in detailInfo.quotaFileList"
                  :key="item.id"
                >
                  <div class="view-images-list" v-if="item.fileType == 3">
                    <div
                      class="delete-img"
                     v-if="type != 0"
                      @click="deleteFlow(index, detailInfo.quotaFileList)"
                    >
                      <i class="el-icon-error"></i>
                    </div>
                    <img
                      v-if="item.fileType == 3"
                      style="width: 150px; height: 150px; margin-right: 10px"
                      :src="item.filePath"
                      alt=""
                      @click="showApplyFlow(item)"
                    />
                  </div>
                </div>
              </div>
              <el-upload
               v-if="type != 0"
                :action="uploadImgUrl"
                list-type="picture-card"
                :on-preview="handlePictureCardPreview"
                :file-list="quotaFileList"
                value-key="img_url"
                :auto-upload="true"
                :show-file-list="false"
                :on-success="handlSuccessFlow"
                :headers="headers"
              >
                <i class="el-icon-plus"></i>
              </el-upload>
            </div>
          </el-descriptions-item>
        </el-descriptions> -->
<!-- 经营信息 -->
        <div class="user_info">
          <el-descriptions title="经营信息" border :column="3">
            <!-- <el-descriptions-item label="经营场所地址">
              {{detailInfo.businessPremisesName}} {{detailInfo.address}}
            </el-descriptions-item> -->

          <el-descriptions-item label="经营场所地址" :span="2">
             <el-cascader
             :disabled="type != 1"
              :value="marketAddressid"
              :options="addressOptions"
              @change="handleChange"
              :props="{ value: 'id',label: 'name',children: 'children'}"
              style="width:50%"
              ref="cascaderAddr"
          ></el-cascader>
          <el-input :disabled="type != 1" v-model="detailInfo.address" style="width:50%" placeholder="请输入详细地址"></el-input>
          </el-descriptions-item>
            <!-- <el-descriptions-item label="养殖数量">
              {{detailInfo.numOfBreeding}}
            </el-descriptions-item> -->
            <el-descriptions-item label="牛数量（头）">
              <el-input :disabled="type != 1" v-model="detailInfo.cattleNum" style="width:100%" placeholder="请输入牛数量"></el-input>
            </el-descriptions-item>
            <el-descriptions-item label="羊数量（只）">
              <el-input :disabled="type != 1" v-model="detailInfo.sheepNum" style="width:100%" placeholder="请输入羊数量"></el-input>
            </el-descriptions-item>

            <!-- <el-descriptions-item label="其它活畜数量">{{detailInfo.otherNum}}</el-descriptions-item> -->
            <el-descriptions-item label="自有草场（亩）">
              <el-input :disabled="type != 1" v-model="detailInfo.ownPasture" style="width:100%" placeholder="请输入自有草场"></el-input>
            </el-descriptions-item>
            <el-descriptions-item label="租用草场（亩）">
              <el-input :disabled="type != 1" v-model="detailInfo.rentPasture" style="width:100%" placeholder="请输入租用草场"></el-input>
            </el-descriptions-item>
            <el-descriptions-item label="棚圈面积（㎡）">
              <el-input :disabled="type != 1" v-model="detailInfo.shedArea" style="width:100%" placeholder="请输入棚圈面积"></el-input>
            </el-descriptions-item>
            <el-descriptions-item label="房屋面积（㎡）">
              <el-input :disabled="type != 1" v-model="detailInfo.housesArea" style="width:100%" placeholder="请输入房屋面积"></el-input>
            </el-descriptions-item>
            <el-descriptions-item label="预计所需饲料（吨）">
              <el-input :disabled="type != 1" v-model="detailInfo.fodderTon" style="width:100%" placeholder="请输入预计所需饲料"></el-input>
            </el-descriptions-item>
            <el-descriptions-item label="预计所需饲草（吨）">
              <el-input :disabled="type != 1" v-model="detailInfo.forageTon" style="width:100%" placeholder="请输入预计所需饲草"></el-input>
            </el-descriptions-item>
            <!-- <el-descriptions-item label="棚圈（间）">{{detailInfo.shedNum}}</el-descriptions-item>
            <el-descriptions-item label="棚圈总价值（万）">{{detailInfo.shedAmtWan}}</el-descriptions-item>
            <el-descriptions-item label="房屋（平米）">{{detailInfo.housesArea}}</el-descriptions-item>
            <el-descriptions-item label="房屋价值（万）">{{detailInfo.housesAmtWan}}</el-descriptions-item>
            <el-descriptions-item label="生产机械（台）">{{detailInfo.prodMachinery}}</el-descriptions-item>
            <el-descriptions-item label="生产机械总价值（万）">{{detailInfo.prodMachineryAmtWan}}</el-descriptions-item>
            <el-descriptions-item label="其它财产信息">{{detailInfo.otherPropertyInfo}}</el-descriptions-item>
            <el-descriptions-item label="其它财产信息总价值 （万）">{{detailInfo.otherPropertyInfoAmtWan}}</el-descriptions-item> -->
          </el-descriptions>
          <div class="user_card">
            <span class="user_title">棚圈照片</span>
            <div class="images" style="display: flex">
              <div class="view-images">
                <div
                  v-for="(item, index) in shedPic"
                  :key="item.id"
                >
                  <div class="view-images-list">
                    <div
                      class="delete-img"
                      v-if="type != 0"
                      @click="deleteAddress(index, shedPic)"
                    >
                      <i class="el-icon-error"></i>
                    </div>
                    <el-image
                      :src="item"
                      :preview-src-list="shedPic"
                      ></el-image>
                    <!-- <img

                      style="width: 150px; height: 150px; margin-right: 10px"
                      :src="item"
                      @click="showApplyAddress(item)"
                    /> -->
                  </div>
                </div>
                <!-- Flow -->
              </div>
              <el-upload
                v-if="type != 0 && shedPic.length < 3"
                :action="uploadImgUrl"
                list-type="picture-card"
                :on-preview="handlePictureCardPreview"
                :file-list="shedPic"
                value-key="img_url"
                :auto-upload="true"
                :show-file-list="false"
                :on-success="handlSuccessAddress"
                :headers="headers"
              >
                <i class="el-icon-plus"></i>
              </el-upload>
            </div>
          </div>
          <div class="user_card">
            <span class="user_title">养殖活畜照片</span>
            <div class="images" style="display: flex">
              <div class="view-images">
                <div
                  v-for="(item, index) in livestockPic"
                  :key="item.id"
                >
                  <div class="view-images-list">
                    <div
                      class="delete-img"
                      v-if="type != 0"
                      @click="deleteAnimal(index, livestockPic)"
                    >
                      <i class="el-icon-error"></i>
                    </div>
                    <!-- <img

                      style="width: 150px; height: 150px; margin-right: 10px"
                      :src="item"
                      @click="showApplyAnimal(item)"
                    /> -->
                    <el-image
                      :src="item"
                      :preview-src-list="livestockPic"
                      ></el-image>
                  </div>
                </div>
                <!-- Flow -->
              </div>
              <el-upload
                v-if="type != 0 && livestockPic.length < 3"
                :action="uploadImgUrl"
                list-type="picture-card"
                :on-preview="handlePictureCardPreview"
                :file-list="livestockPic"
                value-key="img_url"
                :auto-upload="true"
                :show-file-list="false"
                :on-success="handlSuccessAnimal"
                :headers="headers"
              >
                <i class="el-icon-plus"></i>
              </el-upload>
            </div>
          </div>
          <!--  申请人房屋照片-->
         <div class="user_card">
            <span class="user_title">申请人房屋照片</span>
            <div class="images" style="display:flex">
                <div class="view-images">
                  <div v-for="(item,index) in housesPic" :key="item.id">
                    <div class="view-images-list">
                      <div class="delete-img" v-if="type != 0" @click="deleteHouse(index, housesPic)"><i class="el-icon-error"></i></div>
                      <!-- <img style="width:150px; height:150px;margin-right:10px" :src="item" alt="" @click="showApplyHouse(item)"> -->
                      <el-image
                      :src="item"
                      :preview-src-list="housesPic"
                      ></el-image>
                </div>
                  </div>
                 <!-- Flow -->
                 </div>
                     <el-upload
                        v-if="type != 0 && housesPic.length < 3"
                        :action="uploadImgUrl"
                        list-type="picture-card"
                        :on-preview="handlePictureCardPreview"
                        :file-list="housesPic"
                        value-key="img_url"
                        :auto-upload="true"
                        :show-file-list="false"
                        :on-success="handlSuccessHouse"
                        :headers="headers"
						>
							<i class="el-icon-plus"></i>
						</el-upload>
              </div>
        </div>
          <!--  征信照片-->
         <div class="user_card">
            <span class="user_title">征信照片</span>
            <div class="images" style="display:flex">
                <div class="view-images">
                  <div v-for="(item,index) in creditPic" :key="item.id">
                    <div class="view-images-list">
                      <div class="delete-img" v-if="type != 0" @click="deletecredit(index, creditPic)"><i class="el-icon-error"></i></div>
                      <!-- <img style="width:150px; height:150px;margin-right:10px" :src="item" alt="" @click="showCreditPic(item)"> -->
                      <el-image
                      :src="item"
                      :preview-src-list="creditPic"
                      ></el-image>
                </div>
                  </div>
                 <!-- Flow -->
                 </div>
                     <el-upload
                        v-if="type != 0"
                        :action="uploadImgUrl"
                        list-type="picture-card"
                        :on-preview="handlePictureCardPreview"
                        :file-list="creditPic"
                        value-key="img_url"
                        :auto-upload="true"
                        :show-file-list="false"
                        :on-success="handlSuccessCreditPic"
                        :headers="headers"
						>
							<i class="el-icon-plus"></i>
						</el-upload>
              </div>
        </div>
        <div class="user_card">
            <span class="user_title">其它照片</span>
            <div class="images" style="display:flex">
                <div class="view-images">
                  <div v-for="(item,index) in otherPic" :key="item.id">
                    <div class="view-images-list">
                      <div class="delete-img" v-if="type != 0" @click="deleteOther(index, otherPic)"><i class="el-icon-error"></i></div>
                      <!-- <img style="width:150px; height:150px;margin-right:10px" :src="item" alt="" @click="showApplyOther(item)"> -->
                      <el-image
                      :src="item"
                      :preview-src-list="otherPic"
                      ></el-image>
                </div>
                  </div>
                 <!-- Flow -->
                 </div>
                     <el-upload
                        v-if="type != 0 && otherPic.length < 3"
                        :action="uploadImgUrl"
                        list-type="picture-card"
                        :on-preview="handlePictureCardPreview"
                        :file-list="otherPic"
                        value-key="img_url"
                        :auto-upload="true"
                        :show-file-list="false"
                        :on-success="handlSuccessOther"
                        :headers="headers"
						>
							<i class="el-icon-plus"></i>
						</el-upload>
              </div>
        </div>
        </div>
      </div>

    </div>
    <!-- <===== 提示 =====> -->
    <el-dialog
      title="额度申请信息"
      :visible.sync="dialogTableVisible"
      width="80%"
    >
      <el-descriptions
        title=""
        direction="horizontal"
        :column="2"
        border
        :contentStyle="CS"
      >
        <el-descriptions-item label="服务店铺名称" :span="2"
          >{{progressInfo.companyName}}</el-descriptions-item
        >
        <el-descriptions-item label="申请人姓名"
          >{{progressInfo.userName}}</el-descriptions-item
        >
        <el-descriptions-item label="联系电话">{{progressInfo.userPhone}}</el-descriptions-item>
        <el-descriptions-item label="放款单号">{{progressInfo.quotaCode}}</el-descriptions-item>
        <el-descriptions-item label="受理银行">{{progressInfo.bankName}}</el-descriptions-item>
        <el-descriptions-item label="放款金额（元）">{{progressInfo.actualAmt}}</el-descriptions-item> <!-- todo: -->
        <el-descriptions-item label="放款期限（月）">{{progressInfo.bankTerm}} </el-descriptions-item>
        <el-descriptions-item label="利率（年化）"
          >{{progressInfo.quotaRate}}</el-descriptions-item
        >
        <el-descriptions-item label="日期范围">{{progressInfo.quotaStartTime}} ~ {{progressInfo.quotaEndTime}}</el-descriptions-item>
        <el-descriptions-item label="信贷员" :span="2"
          >信贷员</el-descriptions-item
        >
      </el-descriptions>
    </el-dialog>
    <el-dialog
      title=""
      :visible.sync="dialogVisible"
    >
      <img style="width:100%" :src="dialogImageUrl" alt="">
    </el-dialog>
    <el-button v-if="detailInfo.approvalStatus == 2 && type == 0" type="primary" @click="changeType">去审核</el-button>
    <!-- <el-button v-if="detailInfo.approvalStatus == 5" type="primary" @click="handeEnterLoan">确认放款</el-button> -->
  </div>
</template>

<script>
import baseInfo from "../components/baseInfo/index.vue"
import {
  queryApplyDetail,
  changeApproStatus,
  bankList,
  payFQuotaGuarantorList,
  qryParamByType,
  selectBank
} from "@/api/nlbao/guarantee"
import { basicPath2 } from "@/api/base.js"
import { getToken } from "@/utils/auth"
import axios from "axios"
import { getDicts } from "@/api/system/dict/data.js";

export default {
  components: {
    baseInfo
  },
  data() {
    return {
      isShow: false,
      showTitle: "点击查看申请详情",
      icon: "el-icon-arrow-down",
      ruleForm: {},
      rules: {
        approvalStatus: [
          { required: true, message: "请选择审核意见", trigger: "change" }
        ],
        bankUserId: [
          { required: true, message: "请选择放款银行", trigger: "change" }
        ],
        platformAmt: [
          { required: true, message: "请输入平台评估额度", trigger: "blur" }
        ]
      },
      uploadImgUrl:
        process.env.VUE_APP_BASE_API + `${basicPath2}files/obs/fileUpload`, // 上传的图片服务器地址
      headers: {
        Authorization: getToken()
      },
      checkOptions: [{
        value: '信息填写不完善',
        label: '信息填写不完善'
      }, {
        value: '信息填写有误',
        label: '信息填写有误'
      }, {
        value: '资料存在不真实性',
        label: '资料存在不真实性'
      }, {
        value: '所在地区无法办理',
        label: '所在地区无法办理'
      }, {
        value: '其他',
        label: '其他'
      }],
      qryParamByTypeTips: '',
      reasonOther: '',
      orderId: "",
      orderInfo: {},
      orderData: [],
      id: null,
      detailInfo: {},
      dialogVisible: false,
      dialogImageUrl: "",
      hideUpload: true,
      // 法人身份证信息
      faImageList: [],
      envflag: false,
      maxCount: 2, //证件
      // 配偶身份证信息
      wifeList: [],
      wifeFlag: false,
      wifeCount: 2,
      // 申请人征信
      quotaFileList: [],
      commonFlay: false,
      imgUrl: "",
      disabled: false,
      marketAddressid: [],
      addressOptions: [],
      province: "",
      city: "",
      area: "",
      detailAdd: "",
      // addressDetail: "",
      bankOptions: [],
      type: "",
      dialogTableVisible: false,
      CS: {
        width: "380px", //最小宽度
        "word-break": "break-all" //过长时自动换行
      },
      C_S: {
        width: "280px", //最小宽度
        "word-break": "break-all" //过长时自动换行
      },
      progressInfo:{},
      shedPic: [],
      livestockPic: [],
      housesPic: [],
      otherPic: [],
      extsysUserInfo: {},
      guarantorList: [],
      creditPic:[],
      debtExplain: [],
      dataOrderInfoList: [],
      childSituation: [
          { name: '上学', value: 1 },
          { name: '私企上班', value: 2 },
          { name: '公职人员', value: 3 },
          { name: '牧业', value: 4 },
          { name: '个体', value: 5 },
          { name: '无业', value: 6 }
      ],
      tooltips: ''
    }
  },
  filters: {
    // 状态 (0取消 1提交成功，2畜牧师成功，3运营人成功，4服务店铺成功，5银行成功)
    formartSex: (type) =>{
      const statusMap = {
        1: '未婚',
        2: '已婚',
        3: '离异',
        4: '丧偶',
      }
      return statusMap[type]
    }
  },
  computed: {
    handelChildSituation() {
      return (list, value) => {
        let name = "";
        list.forEach((item) => {
          if (item.value == value) {
            name = item.name;
          }
        });
        return name;
      };
    },
  },
  created() {
    this.extsysUserInfo = JSON.parse(window.localStorage.getItem('extsysUserInfo'))
    // this.getBankList()
    this.getAddressList()
    this.getPayFQuotaGuarantorList()
    this.getqryParamByType()
    this.id = this.$route.query.id
    this.type = this.$route.query.type
    this.type == 0 ? (this.isShow = true) : (this.isShow = false)
    this.getQueryApplyDetail()
  },
  mounted() {},
  methods: {
    // 获取地址信息
    async getAddressList() {
      const { data: res } = await axios.get("/areaArray.json")
      this.addressOptions = res
    },
    getPayFQuotaGuarantorList() {
        payFQuotaGuarantorList({
            pageNum: 1,
            pageSize: 1000
        }).then(res => {
          this.guarantorList = res.data.list
        })
    },

    async getQueryApplyDetail() {
      const { data: res } = await queryApplyDetail({ quotaId: Number(this.id) })
      this.detailInfo = res
      let addressArr = res.businessPremises.split(',')
      this.marketAddressid = addressArr.map(item => {
        return +item;
      });
      this.shedPic = this.detailInfo.shedPic ? this.detailInfo.shedPic.split(',') : [];
      console.log('shedPic', this.shedPic)
      this.livestockPic = this.detailInfo.livestockPic ? this.detailInfo.livestockPic.split(',') : [];
      this.housesPic = this.detailInfo.housesPic ? this.detailInfo.housesPic.split(',') : [];
      this.otherPic = this.detailInfo.otherPic ? this.detailInfo.otherPic.split(',') : [];
      this.creditPic = this.detailInfo.creditPic ? this.detailInfo.creditPic.split(',') : [];
      this.debtExplain = JSON.parse(this.detailInfo.debtExplain)
      if (this.detailInfo.orderInfo) {
        this.dataOrderInfoList = JSON.parse(this.detailInfo.orderInfo).goodsModel
      }
      if (this.detailInfo.userCertPic1) {
        this.faImageList.push({
          url: this.detailInfo.userCertPic1,
          type:1
        })
      }
      if (this.detailInfo.userCertPic2) {
        this.faImageList.push({
          url: this.detailInfo.userCertPic2,
          type:2
        })
      }
      if(this.detailInfo.spouseIdPic1) {
        this.wifeList.push({
          url: this.detailInfo.spouseIdPic1
        })
      }
      if(this.detailInfo.spouseIdPic2) {
        this.wifeList.push({
          url: this.detailInfo.spouseIdPic2
        })
      }
      getDicts('nyb_quota_fodder_conf').then((res) => {
        const fodderConf =res.data || []
          let sheepValue = 0;
          let cattleValue = 0;
          let sheepPrice = 0;
          let cattlePrice = 0;
          fodderConf.forEach(item => {
            if(item.dictLabel == 1) {
              sheepValue = item.dictValue * this.detailInfo.sheepNum
              sheepPrice = item.dictValue
            }
            if(item.dictLabel == 2){
              cattleValue = item.dictValue * this.detailInfo.cattleNum
              cattlePrice = item.dictValue
            }
          })
        setTimeout(() => {
          const totals = (+cattleValue + +sheepValue).toFixed(2)
          this.tooltips = `
            该用户饲草料年需求金预计为${totals}元
            <br/>
            计算规则：牛只一年一头预计消耗饲草料${cattlePrice}元，
                    羊只一年一只预计消耗饲草料${sheepPrice}元，
                    申请人当前牛只存栏${this.detailInfo.cattleNum}头，羊只存栏${this.detailInfo.sheepNum}只，
                    合计金额=${cattlePrice}*${this.detailInfo.cattleNum}+${sheepPrice}*${this.detailInfo.sheepNum}=${totals}元
          `
        }, 3000)
      })
    },
    handeEnterLoan(row, type){
      this.$router.push({
          path: '/operate/enterLoan',
          query: {
            id: this.detailInfo.id,
            type: 0,
            quotaCode: this.detailInfo.quotaCode,
          }
        })
    },
    changeType() {
      this.type = 1
      this.type == 0 ? (this.isShow = true) : (this.isShow = false)
    },
    // 申请人及配偶身份证上传
    deleteimg(index, fileList) {
      this.$nextTick(() => {
        fileList.splice(index, 1)
        this.faImageList = fileList
        this.envflag = !(fileList.length >= this.maxCount)
      })
    },
    changType() {
      if (this.detailInfo.type == 2 && this.detailInfo.applyType == 4) {
        this.detailInfo.applyType = ''
      }
    },
    showimage(file) {
      console.log(file)
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    handlEnvironmentSuccess(res, file, fileList) {
      this.faImageList.push({
        url: res.result[0].objectUrl
      })
      this.$nextTick(() => {
        this.envflag = !(fileList.length >= this.maxCount)
      })
    },
    deleteWifeImg(index, fileList) {
      fileList.splice(index, 1)
      this.wifeList = fileList
      this.$nextTick(() => {
        this.wifeFlag = !(fileList.length >= this.maxCount)
      })
    },
    showWifeImage(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    handlWifeSuccess(res, file, fileList) {
      this.wifeList.push({
        url: res.result[0].objectUrl
      })
      this.$nextTick(() => {
        this.wifeFlag = !(fileList.length >= this.maxCount)
      })
    },
    handlePictureCardPreview() {},
    // 申请人征信上传
    deleteApply(index, fileList) {
      fileList.splice(index, 1)
      this.livestockPic = fileList
    },
    showApplyImage(file) {
      /* console.log(file) */
      this.dialogImageUrl = file.filePath
      this.dialogVisible = true
    },
    handlSuccessApply(res, file, fileList) {
      this.detailInfo.quotaFileList.push({
        fileType: 1,
        fileName: file.url,
        filePath: res.result[0].objectUrl
      })
    },
    // 配偶征信信息
    deleteApplyWife(index, fileList) {
      /* console.log(index)
      console.log(fileList) */
      fileList.splice(index, 1)
      this.detailInfo.quotaFileList = fileList
    },
    showApplyImageWife(file) {
      /* console.log(file) */
      this.dialogImageUrl = file.filePath
      this.dialogVisible = true
    },
    showApplyHouse(file) {
      this.dialogImageUrl = file
      this.dialogVisible = true
    },
    showCreditPic(file) {
      this.dialogImageUrl = file
      this.dialogVisible = true
    },
    showApplyOther(file) {
      this.dialogImageUrl = file
      this.dialogVisible = true
    },
    handlSuccessApplyWife(res, file, fileList) {
      this.detailInfo.quotaFileList.push({
        fileType: 2,
        fileName: file.url,
        filePath: res.result[0].objectUrl
      })
    },
    // 申请人近12个月银行流水记录
     deleteFlow(index, fileList) {
      /* console.log(index)
      console.log(fileList) */
      fileList.splice(index, 1)
      this.detailInfo.quotaFileList = fileList
    },
    showApplyFlow(file) {
      // console.log(file)
      this.dialogImageUrl = file.filePath
      this.dialogVisible = true
    },
    handlSuccessFlow(res, file, fileList) {
      this.detailInfo.quotaFileList.push({
        fileType: 3,
        fileName: file.url,
        filePath: res.result[0].objectUrl
      })
    },
    // 经营场所场地照片
    deleteAddress(index, fileList) {
      fileList.splice(index, 1)
      this.shedPic = fileList
    },
    showApplyAddress(file) {
      this.dialogImageUrl = file
      this.dialogVisible = true
    },
    handlSuccessAddress(res, file, fileList) {
      // this.detailInfo.quotaFileList.push({
      //   fileType: 4,
      //   fileName: file.url,
      //   filePath: res.result[0].objectUrl
      // })
      console.log(file.response.result[0].objectUrl)
      this.shedPic.push(file.response.result[0].objectUrl)
      console.log('shedPic======>>>>',this.shedPic)
    },
    handlSuccessHouse(res, file, fileList) {
      // this.detailInfo.quotaFileList.push({
      //   fileType: 4,
      //   fileName: file.url,
      //   filePath: res.result[0].objectUrl
      // })
      console.log(file)
      this.housesPic.push(file.response.result[0].objectUrl)
    },
    handlSuccessOther(res, file, fileList) {
      // this.detailInfo.quotaFileList.push({
      //   fileType: 4,
      //   fileName: file.url,
      //   filePath: res.result[0].objectUrl
      // })
      console.log(file.response)
      this.otherPic.push(file.response.result[0].objectUrl)
    },
    handlSuccessCreditPic(res, file, fileList) {
      this.creditPic.push(file.response.result[0].objectUrl)
    },
    // 养殖活畜照片
    deleteAnimal(index, fileList) {
      fileList.splice(index, 1)
      this.livestockPic = fileList
    },
    deleteHouse(index, fileList) {
      fileList.splice(index, 1)
      this.housesPic = fileList
    },
    deleteOther(index, fileList) {
      fileList.splice(index, 1)
      this.otherPic = fileList
    },
    deletecredit(index, fileList) {
      fileList.splice(index, 1)
      this.creditPic = fileList
    },
    showApplyAnimal(file) {
      this.dialogImageUrl = file
      this.dialogVisible = true
    },
    handlSuccessAnimal(res, file, fileList) {
      this.livestockPic.push(file.response.result[0].objectUrl)
    },
    cancel() {
      this.commonFlay = false
      this.disabled = false
      this.envflag = false
      this.wifeFlag = false
    },
    // 选择状态
    handleChangeApprovalStatus(value) {
      this.ruleForm.reason = ''
      this.ruleForm = JSON.parse(JSON.stringify(this.ruleForm))
      this.reasonOther = ''
    },
    getqryParamByType() {
      qryParamByType({
        paramType: 'QUOTA_FROZEN'
      }).then(res => {
        this.qryParamByTypeTips = res.data.PayBaseParamList[0].paramValue
      })
    },
    // 提交申请
    handelSubmitForm() {
      if (this.ruleForm.approvalStatus == 3) {
        const params = {
          id: Number(this.id),
          operatorId: this.extsysUserInfo.access_token,
          ...this.ruleForm,
          type: this.detailInfo.type,
          cattleNum: this.detailInfo.cattleNum,
          sheepNum: this.detailInfo.sheepNum,
          ownPasture: this.detailInfo.ownPasture,
          rentPasture: this.detailInfo.rentPasture,
          shedArea: this.detailInfo.shedArea,
          housesArea: this.detailInfo.housesArea,
          fodderTon: this.detailInfo.fodderTon,
          forageTon: this.detailInfo.forageTon,
          shedPic: this.shedPic && this.shedPic.join(','),
          livestockPic: this.livestockPic && this.livestockPic.join(','),
          housesPic: this.housesPic && this.housesPic.join(','),
          otherPic: this.otherPic && this.otherPic.join(','),
          creditPic: this.creditPic && this.creditPic.join(','),
          applyType: this.detailInfo.type == 2 ? this.detailInfo.applyType : 4,
          guarId: this.detailInfo.type == 1 ? this.detailInfo.guarId : '',
          businessPremises: this.detailAdd
        }
        if (this.ruleForm.reason == '其他') {
          params.reason = this.reasonOther
        }
        this.$refs.ruleForm.validate((valid) => {
          if (valid) {
                this.$confirm('是否确认通过审核', '提示', {
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                  type: 'warning'
                }).then(() => {
                  changeApproStatus(params).then((res) => {
                    if (res.stautscode == 200) {
                      this.$message.success("操作成功")
                      this.$router.go(-1)
                    } else if(res.stautscode === 2016) {
                      this.progressInfo = res.data
                      this.$confirm(
                        `${res.data.userName} 在 ${res.data.bankName} 有进行中的专项额度申请，不可重复申请。`,
                        "提示",
                        {
                          confirmButtonText: "查看信息",
                          cancelButtonText: "关闭",
                          type: "warning"
                        }
                      ).then(() => {
                      /*   this.$message({
                          type: "success",
                          message: "查看!"
                        }) */
                        this.dialogTableVisible = true
                      })
                    } else {
                      this.$message.error(res.msg)
                    }
                })
            }).catch(() => {

            })
          }
        })
      } else if (this.ruleForm.approvalStatus == 6) {
        const params = {
          id: Number(this.id),
          reason: this.ruleForm.reason,
          approvalStatus: this.ruleForm.approvalStatus,
          operatorId: this.extsysUserInfo.access_token,
        }
        if (this.ruleForm.reason == '其他') {
          params.reason = this.reasonOther
        }
        this.$refs.ruleForm.validate((valid) => {
          if (valid) {
            if (this.ruleForm.approvalStatus === 6) {
              if (!this.ruleForm.reason || this.ruleForm.reason == '其他' && !this.reasonOther) {
                this.$message.error("审核意见不能为空")
              } else {
                this.$confirm('是否确认驳回，驳回后，申请人可重新提交', '提示', {
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                  type: 'warning'
                }).then(() => {
                  changeApproStatus(params).then((res) => {
                    // console.log(res)
                    if (res.stautscode == 200) {
                      this.$message.success("驳回成功")
                      this.commonFlay = false
                      this.disabled = false
                      this.envflag = false
                      this.wifeFlag = false
                      this.$router.go(-1)
                    } else {
                      this.$message.error(res.msg);
                    }
                  })
                }).catch(() => {

                })
              }
            }
          }
        })
      } else if (this.ruleForm.approvalStatus == 11) {
         this.$confirm(this.qryParamByTypeTips, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            const params = {
              id: Number(this.id),
              reason: this.ruleForm.reason,
              approvalStatus: this.ruleForm.approvalStatus,
              operatorId: this.extsysUserInfo.access_token,
            }
            if (this.ruleForm.reason == '其他') {
              params.reason = this.reasonOther
            }
            this.$refs.ruleForm.validate((valid) => {
              if (valid) {
                if (this.ruleForm.approvalStatus === 11) {
                  if (!this.ruleForm.reason || this.ruleForm.reason == '其他' && !this.reasonOther) {
                    this.$message.error("审核意见不能为空")
                  } else {
                    changeApproStatus(params).then((res) => {
                      // console.log(res)
                      if (res.stautscode == 200) {
                        this.$message.success("操作成功！")
                        this.commonFlay = false
                        this.disabled = false
                        this.envflag = false
                        this.wifeFlag = false
                        this.$router.go(-1)
                      } else {
                        this.$message.error(res.msg);
                      }
                    })
                  }
                }
              }
            })
          }).catch(() => {

          });
      }
    },
    handelShow() {
      this.isShow = !this.isShow
      this.isShow == true
        ? (this.showTitle = "收起申请详情")
        : (this.showTitle = "展开查看申请详情")
      this.isShow == true
        ? (this.icon = "el-icon-arrow-up")
        : (this.icon = "el-icon-arrow-down")
    },

    // 启用/禁用编辑
    handelEdit() {
      this.disabled = true
      this.hideUpload = false
      this.commonFlay = true
    },

    // 三级地址选择
    handleChange(value) {
      // console.log(value)
      if (value && value.length != 0) {
        let arr = this.$refs["cascaderAddr"].getCheckedNodes()[0].pathLabels
        console.log(arr)
        this.detailAdd = arr.join(",")
        this.province = arr[0]
        this.city = arr[1]
        this.area = arr[2]
      }
    },
    remoteMethod(query) {
      console.log(query)
      const params = {
        pageNum: 1,
        pageSize: 100,
        vendorType: 1,
        specialRole: 3,
        platformId: this.$store.state.user.user.platformId,
        companyName: query
      }
      // bankList(params).then((res) => {
      //   // console.log("=========>", res)
      //   const options = []
      //   if (res?.data?.list) {
      //     res.data.list.forEach((item, index) => {
      //       options.push({
      //         label: item.userName,
      //         value: item.userId
      //       })
      //     })
      //     this.bankOptions = options
      //   }
      // })

        selectBank({
          pageNum: 1,
          pageSize: 1000,
          companyName: query
        }).then((res) => {
          if (res.stautscode == 200) {
              const data = res.data;
              data.list.map(item => {
                  item.text = item.companyName;
                  item.value = item.userId;
                  return item;
              });
              this.bankOptions = data.list
          } else{
            this.$message.error(res.msg);
          }
        })
    }
  }
}
</script>

<style scoped lang="scss">
:deep(.el-image){
  img{
    width: 150px;
    height: 150px;
  }
}
.is_show {
  width: 100%;
  text-align: center;
  color: rgb(22, 155, 213);
  cursor: pointer;
}
::v-deep .el-steps--horizontal {
  width: 80%;
}
.step_style {
  width: 100%;
  margin: 10px 0;
}
::v-deep .el-descriptions__header {
  background-color: rgb(242, 242, 242);
  padding: 0 20px;
  margin: 30px 0 10px 0;
  height: 40px;
}

.user_info {
  .user_card {
    .user_title {
      margin: 10px 0;
      display: inline-block;
      color: rgb(153, 153, 153);
      font-size: 14px;
    }
    .images {
      display: flex;
      flex-wrap: wrap;
      .el-image {
        width: 150px;
        height: 150px;
        margin-right: 20px;
        border-radius: 5px;
      }
    }
  }
}

.view-images {
  display: flex;
  flex-wrap: wrap;
  .view-images-list {
    width: 146px;
    height: 146px;
    display: inline-block;
    margin: 0 10px 0 10px;
    position: relative;
    .delete-img {
      display: inline-block;
      font-size: 24px;
      cursor: pointer;
      color: rgba(0, 0, 0, 0.5);
      position: absolute;
      top: -16px;
      right: -9px;
      z-index: 999;
    }
    img {
      width: 100%;
      height: 100%;
      border-radius: 10px;
      cursor: pointer;
    }
  }
}
</style>
