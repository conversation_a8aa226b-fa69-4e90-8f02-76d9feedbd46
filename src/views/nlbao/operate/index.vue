<template>
  <div class="app-container">
    <el-card class="mb10 form_box" shadow="never" ref="formBox">
      <!-- <Form
        :showSearch="showSearch"
        :searchList="searchList"
        @getList="getList"
      ></Form> -->

      <el-form
        :model="queryParams"
        ref="queryForm"
        size="small"
        :inline="true"
        v-show="showSearch"
      >
      <el-row class="form_row">
        <el-col class="form_col">
          
          <el-form-item
            v-for="(item, index) in searchList"
            :key="index"
            :label="item.name"
            :prop="item.value"
          >
            <el-input
              v-if="item.type === 'text'"
              v-model="queryParams[item.value]"
              :placeholder="'请输入' + item.name"
              clearable
              style="width: 240px"
            />
            <el-select
              v-if="item.type === 'select'"
              v-model="queryParams[item.value]"
              :placeholder="'请选择' + item.name"
              clearable
              style="width: 240px"
            >
              <el-option
                v-for="item in item.options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <el-date-picker
              v-if="item.type === 'daterange'"
              v-model="queryParams[item.value]"
              style="width: 240px"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            ></el-date-picker>
            <el-cascader
              v-if="item.type === 'cascader' && options.length > 0"
              v-model="queryParams[item.value]"
              style="width: 240px"
            :options="options"
            ></el-cascader>
          </el-form-item>
          <!-- <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
              >重置</el-button
            >
          </el-form-item> -->

          </el-col>
        </el-row>
        <el-row>
            <el-col >
                <el-form-item >
                  <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                  <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                  <template v-if="toggleSearchDom">
                    <el-button type="text" @click="packUp" >
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <i
                      :class="{ 'el-icon-arrow-down': !toggleSearchStatus, 'el-icon-arrow-up': toggleSearchStatus }"
                    ></i>
                  </el-button>
                  </template>
                
                </el-form-item>
              </el-col>
            </el-row>
      </el-form>
    </el-card>
    <el-card shadow="never" class="list_table">
        <el-row class="mb8 form_btn">
          <el-col  class="form_btn_col">
            <el-button
              icon="el-icon-download"
              type="primary"
              size="mini"
              @click="handelExport"
              >导出记录</el-button
            >
          <!--    <el-button
              icon="el-icon-download"
              type="warning"
              size="mini"
              @click="exportExcel"
              >导出excel</el-button
            >
          </el-col> -->
          </el-col>
          <el-col>
          <!-- <el-button
              icon="el-icon-edit"
              type="primary"
              size="mini"
              @click="handelInsert"
              >新增额度</el-button
            > -->
          </el-col>
      <!-- <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar> -->
    </el-row>
     
    <el-table v-loading="loading" :data="tableData"  :height="tableHeight" border>
      <!-- <el-table-column align="center" type="selection" width="55" /> @selection-change="handleSelectionChange" -->
      <el-table-column align="center" label="编号" prop="roleId" width="50" fixed />
      <el-table-column
        fixed
        align="center"
        label="操作"
        :show-overflow-tooltip="true"
        width="180"
      >
      <template slot-scope="scope">
        <el-button v-if="scope.row.approvalStatus == 2" size="mini" type="text" @click="handelShenHe(scope.row, 1, '/operate/examine')">审核</el-button>
        <el-button size="mini" type="text" @click="handelShenHe(scope.row, 0, '/operate/examine', scope.row.type)">详情</el-button>
        <el-button size="mini" type="text" v-if="scope.row.approvalStatus == 4" @click="rollback(scope.row)">撤销</el-button>
        <el-button size="mini" type="text" v-if="scope.row.approvalStatus == 6" @click="addBlackFn(scope.row)">加入黑名单</el-button>
        <el-button size="mini" type="text" v-if="scope.row.approvalStatus == 7" @click="handeSet(scope.row, 0, '/operate/repaymentInfo', scope.row.type)">还款记录</el-button>
        <el-button size="mini" type="text" v-if="scope.row.approvalStatus == 7" @click="handeSet(scope.row, 1, '/operate/payInfo', scope.row.type)">消费记录</el-button>
        <el-button size="mini" type="text" v-if="scope.row.approvalStatus == 5" @click="handeEnterLoan(scope.row, 0, '/operate/enterLoan')">确认放款</el-button>

      </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="审核状态"
        prop="approvalStatus"
        width="120"
      >
      <template slot-scope="scope">
      <div>
          <div>{{scope.row.approvalStatusName}}</div>
       </div>
      </template>
      </el-table-column>

      <el-table-column align="center" label="申请时间" prop="createTime" width="160" />
      <el-table-column align="center" label="申请人" prop="userName" :show-overflow-tooltip="true" width="150" />
      <el-table-column align="center" label="联系电话" prop="userPhone" :show-overflow-tooltip="true" width="150" ></el-table-column>
      <el-table-column align="center" label="经营区域" width="180" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{scope.row.businessPremisesName}}{{scope.row.address}}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="申请主体" prop="userTypeName" :show-overflow-tooltip="true" width="130" />
      <el-table-column align="center" label="申请方式" prop="applyTypeName" :show-overflow-tooltip="true" width="130" />
      <el-table-column align="center" label="额度类型" prop="typeName" :show-overflow-tooltip="true" width="130" >
      </el-table-column>
      <el-table-column align="center" width="150" label="申请额度(万)" prop="applyAmtWan" :show-overflow-tooltip="true"/>
      <el-table-column align="center" label="推荐企业" prop="companyName" :show-overflow-tooltip="true" width="150"/>
      <el-table-column align="center" label="推荐人名称" prop="referrer" :show-overflow-tooltip="true" width="150"/>
       <!-- <el-table-column
        align="center"
        label="证件号"
        prop="userIdNo"
        :show-overflow-tooltip="true"
        width="200"
      >
      </el-table-column> -->
       <!-- <el-table-column
        align="center"
        label="申请类型"
        prop="type"
      >
      <template slot-scope="scope">
        <span>{{scope.row.type | venterType}}</span>
         <dict-tag :options="dict.type.nyb_loan_type" :value="scope.row.type" />
      </template>
      </el-table-column> -->
      <!-- <el-table-column align="center"
        label="申请期限(月)"
        prop="quotaTerm"
        :show-overflow-tooltip="true"
        width="150"
      />
       <el-table-column
        align="center"
        label="推荐企业"
        prop="companyName"
        :show-overflow-tooltip="true"
        width="150"
      />
       <el-table-column
        align="center"
        label="放款单号"
        prop="quotaCode"
        :show-overflow-tooltip="true"
        width="150"
      >
      <template slot-scope="scope">
        <span v-if="scope.row.approvalStatus ==7">{{scope.row.quotaCode}}</span>
      </template>
       </el-table-column>
       <el-table-column
        align="center"
        label="放款银行"
        prop="bankName"
        :show-overflow-tooltip="true"
        width="150"
      />
      <el-table-column
        align="center"
        label="放款金额(元)"
        width="150"
        prop="actualAmt"
      />
      <el-table-column
        align="center"
        label="放款期限(月)"
        width="150"
        prop="bankTerm"
      />
      <el-table-column
        align="center"
        label="利率（年化）"
        prop="quotaRate"
        width="120"
      >
      </el-table-column>
      <el-table-column
        align="center"
        label="处理状态"
        prop="approvalStatus"
        width="120"
      >
      <template slot-scope="scope">
       <el-tag :type="scope.row.approvalStatus == 2 ? 'success': 'default'">
          <span>{{scope.row.approvalStatus | alreadyStatus}}</span>
        </el-tag>
      </template>
      </el-table-column> -->
    </el-table>
    <pagination
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    >
    </pagination>
    </el-card>
    <personal ref="personal"></personal>
    <AddBlackList ref="addBlackList" :id="currentId" @getList="getList"></AddBlackList>
  </div>
</template>

<script>
import axios from "axios"
import personal from '../operate/personal.vue'
import { SecuredList, exportQuotaRecord, empayRollback } from '@/api/nlbao/guarantee'
import { fileStreamToFile } from "@/utils/exportQuot.js";
import AddBlackList from "./addBlackList.vue";
import { tableUi } from "@/utils/mixin/tableUi.js";

export default {
  mixins: [tableUi],
  dicts: ["nyb_loan_type"],
  data() {
    return {
      showSearch: true,
      searchList: [
        {
          name: "审核状态",
          value: "approvalStatus",
          type: "select",
          options: [
            { label: "全部", value: 0 },
            { label: "等待平台审核", value: 2 },
            { label: "等待推荐企业审核", value: 3 },
            { label: "等待银行审核", value: 4 },
            { label: "等待平台确认", value: 5 },
            { label: "审核成功", value: 7 },
            { label: "审核失败", value: 6 },
          ]
        },
        { name: "申请人", value: "userName", type: "text" },
        { name: "联系电话", value: "userPhone", type: "text" },
        { name: "申请时间", value: "createTime", type: "daterange" },
        {
          name: "申请主体",
          value: "userType",
          type: "select",
          options: [
            { label: "全部", value: '' },
            { label: "个人", value: 3 },
            { label: "企业", value: 1 }
          ]
        },
        {
          name: "申请方式",
          value: "applyType",
          type: "select",
          options: [
            { label: '活畜抵押', value: 1 },
            { label: '信用额度', value: 2 },
            { label: '企业推荐', value: 4 },
            { label: '无货质押', value: 3 },
            { label: '仓单质押', value: 5 },
            { label: '粮仓无货质押', value: 6 },
            { label: '粮仓仓单质押', value: 7 },
          ]
        },
        {
          name: "额度类型",
          value: "type",
          type: "select",
          options: [
            { label: "全部", value: '' },
            { label: '专项额度', value: 1 },
            { label: '通用额度', value: 2 }
          ]
        },
        { name: "推荐企业", value: "companyName", type: "text" },
        { name: "推荐人名称", value: "referrer", type: "text" },
        { name: "经营区域", value: "businessPremises", type: "cascader" },

        // { name: "申请人身份证号", value: "userIdNo", type: "text" },
        // { name: "放款银行", value: "bankName", type: "text" },
        // {
        //   name: "处理状态",
        //   value: "status",
        //   type: "select",
        //   options: [
        //     { label: "待处理", value: '2,4,5'},
        //     { label: "已处理", value: '3,6,7' }
        //   ]
        // }
      ],
        loading: true,
      // 表格数据
      tableData: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      searchParams:{},
      searchstatus:'',
      extsysUserInfo: {},
      exportData: {},
      currentId: '',
      options: {}
    }
  },
  components: {
    personal,
    AddBlackList
  },
  filters: {            
    // 状态 (0取消 1提交成功，2畜牧师成功，3运营人成功，4服务店铺成功，5银行成功)
    alreadyStatus: (type) =>{
      const statusMap = {
        0: '待处理',
        1: '待处理',
        2: '待处理',
        3: '已处理',
        4: '已处理',
        5: '待处理',
        6: '已处理',
        7: "已处理"
      }
      return statusMap[type]
    },
    venterType: (type) => {
       const statusMap = {
        1: '个人',
        2: '企业'
      }
      return statusMap[type]
    }
  },
  created() {
    this.getAddressList()
    this.extsysUserInfo = JSON.parse(window.localStorage.getItem('extsysUserInfo'))
    this.getList();
  },
  methods: {
       async getList(data) {
         /*  const status = '2,3,4,5,6,7'
          const approvalStatus = data?.approvalStatus ? data?.status: status; */
          if(data?.approvalStatus || this.searchParams.approvalStatus){
            this.searchstatus = data.approvalStatus || this.searchParams.approvalStatus
          } else if(data?.status || this.searchParams.status) {
             this.searchstatus = data.status || this.searchParams.status
          } else if(data?.approvalStatus && data?.status) {
            this.searchstatus = data.approvalStatus
          } else {
             this.searchstatus = '2,3,4,5,6,7'
          }
         if(data) {
            this.searchParams = {
              userName : data.userName || this.searchParams.userName,
              userPhone: data.userPhone || this.searchParams.userPhone,
              userIdNo: data.userIdNo || this.searchParams.userIdNo,
              companyName: data.companyName || this.searchParams.companyName,
              bankName: data.bankName || this.searchParams.bankName,
              userType: data.userType || this.searchParams.userType,
              startTime: data.createTime && data.createTime[0] || this.searchParams.startTime,
              endTime: data.createTime && data.createTime[1] || this.searchParams.endTime, 
              businessPremises: data.businessPremises ? data.businessPremises.join(',') : '' || this.searchParams.businessPremises, 
              referrer: data.referrer || this.searchParams.referrer,
              type: data.type || this.searchParams.type,
              applyType: data.applyType || this.searchParams.applyType
         }
         }
          this.loading = true;
          this.exportData = {...this.queryParams, ...this.searchParams,  approvalStatusArray: this.searchstatus,
            platformId: this.$store.state.user.user.platformId}
          delete this.exportData.createTime;
          const res = await SecuredList(this.exportData);
          if(res.stautscode===200) {
            this.loading = false;
            this.tableData = res.data.list;
            this.total = res.data.totalRow;
            this.tableData.map((item, index) => {
              item.roleId = index + 1;
              item.userTypeName = item.userType == 1 ? '企业' : '个人'
              // item.applyTypeName = item.applyType == 1 ? '抵押' : '企业推荐'
              // item.typeName = item.type == 1 ? '专项额度' : '通用额度'
              let applyAmtWan = (item.applyAmt / 10000).toFixed(4);
              if (parseInt(applyAmtWan) == applyAmtWan) {
                applyAmtWan = Number(applyAmtWan).toFixed(0)
              }
              item.applyAmtWan = applyAmtWan;
              return index
            })
          } else {
            this.$message.error(res.msg)
          }
          
    },
   /*  exportRecords() {
      console.log("exportRecords")
    },
    exportExcel() {
      console.log("exportExcel")
    },
 */
    // 多选框选中数据
    handleSelectionChange(selection) {
      console.log(selection)
      this.ids = selection.map((item) => item.roleId)
      this.single = selection.length != 1
      this.multiple = !selection.length
    },
    handeSet(row, type, path, userType) {
      console.log(userType)
      if(userType==1) {
         this.$router.push({
          path: path,
          query: {
            id: row.quotaId,
            type: type,
            quotaCode: row.quotaCode,
          }
        })
      } else if(userType==1 || path=="/operate/repaymentInfo"){
         this.$router.push({
          path: path,
          query: {
            id: row.quotaId,
            type: type,
            quotaCode: row.quotaCode,
          }
        })
      }else if(userType==1 || path=="/operate/payInfo"){
         this.$router.push({
          path: path,
          query: {
            id: row.quotaId,
            type: type,
            quotaCode: row.quotaCode,
          }
        })
      } else {
         this.$router.push({
          path: '/operate/enterpriseDetail',
          query: {
            id: row.quotaId,
            type: type,
            quotaCode: row.quotaCode,
          }
        })
      }
       
    },
    handelExport(){
      exportQuotaRecord({
        ...this.exportData
      }).then((res) => {
        fileStreamToFile('额度记录',res)
      })
    },
    handelShenHe(row,type){
       this.$router.push({
          path: '/operate/examine',
          query: {
            id: row.quotaId,
            type: type,
            quotaCode: row.quotaCode,
          }
        })
    },
    handeEnterLoan(row, type){
      this.$router.push({
          path: '/operate/enterLoan',
          query: {
            id: row.quotaId,
            type: type,
            quotaCode: row.quotaCode,
          }
        })
    },
    async getAddressList() {
      const { data: res } = await axios.get("/city.json")
      this.options = res
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.queryParams.pageSize = 10
      this.getList( {
        ...this.queryParams
      })
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 新增记录
    handelInsert() {
      this.$refs.personal.dialogFormVisible = true
    },
    addBlackFn(row) {
      this.$refs.addBlackList.handleOpen()
      this.currentId = row.id
    },
    rollback(row) {
      this.$confirm('确认撤销该条数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          empayRollback({
            quotaCode: row.quotaCode
          }).then(res => {
            if(res.stautscode ==200) {
              this.$message.success("操作成功");
              this.getList();
            } else {
              this.$message.error(res.msg)
            }
          })
        }).catch(() => {        
        });
    }
  }
}
</script>
<style lang="scss" scoped></style>
