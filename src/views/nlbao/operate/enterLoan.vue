<template>
  <div class="app-container">
    <div class="table">
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-width="180px"
        class="demo-ruleForm"
        v-if="type == 0"
      >
        <el-form-item label="申请人申请额度">
          <div><span>
            {{ detailInfo.applyAmt ? detailInfo.applyAmt : '-' }}</span> 
            <span v-if="detailInfo.applyAmt">元</span> 
          </div>
          <!-- <el-popover
            width="200"
            trigger="hover"
            content="这是一段内容,这是一段内容,这是一段内容,这是一段内容。">
            <i slot="reference" class='el-icon-info'></i>
          </el-popover> -->
        </el-form-item>
        <el-form-item label="平台评估额度">
          <div><span>{{ detailInfo.platformAmt ? detailInfo.platformAmt : '-'}}</span> <span v-if="detailInfo.platformAmt">元</span></div>
        </el-form-item>
        <!-- <el-form-item label="推荐企业评估额度" v-if="detailInfo.guarId">
          <div><span>{{ detailInfo.guarAmt ? detailInfo.guarAmt :'-'}}</span> <span v-if="detailInfo.guarAmt">元</span></div>
        </el-form-item> -->
         <!-- <el-form-item label="银行评估额度">
          <div><span>{{ detailInfo.bankAmt? detailInfo.bankAmt : '-' }}</span> <span v-if="detailInfo.bankAmt">元</span></div>
        </el-form-item> -->

        <!-- <el-form-item label="审核意见" prop="approvalStatus">
          <el-radio-group
            v-model="ruleForm.approvalStatus"
            @change="handelChangeStatus"
          >
            <el-radio :label="7">审核通过</el-radio>
            <el-radio :label="6">审核不通过</el-radio>
          </el-radio-group>
        </el-form-item> -->
        <el-form-item label="额度类型" prop="type">
          <el-radio-group
            v-model="ruleForm.type"
          >
            <el-radio :label="2">通用额度</el-radio>
            <el-radio :label="1">专项额度</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="" prop="type">
          <span v-if="ruleForm.type == 2">通用额度：可在平台所有支持额度结算的业务场景使用</span>
          <span v-if="ruleForm.type == 1">专项额度：仅能在指定店铺或场景下使用。请确认申请人与推荐企业已沟通清楚使用场景。</span>
        </el-form-item>
        
        <el-form-item label="申请方式" prop="type" v-if="ruleForm.type == 2">
          <el-radio-group
            v-model="ruleForm.applyType"
          >
            <el-radio :label="1">活畜抵押</el-radio>
            <el-radio :label="2">信用额度</el-radio>
            <el-radio v-if="detailInfo.userType != 3" :label="3">无货质押</el-radio>
            <el-radio :label="4">企业推荐</el-radio>
            <el-radio v-if="detailInfo.userType != 3" :label="5">仓单质押</el-radio>
            <el-radio :label="6">粮仓无货质押</el-radio>
            <el-radio v-if="detailInfo.userType != 3" :label="7">粮仓仓单质押</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="推荐企业" prop="type" v-if="ruleForm.type == 1">
            <el-select v-model="ruleForm.guarId"
              style="width: 360px">
            <el-option
              v-for="(item) in guarantorList"
              :key="item.id"
              :label="item.companyName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="审核意见" prop="reason" v-if="ruleForm.approvalStatus==6">
          <el-input
            type="textarea"
            v-model="ruleForm.reason"
            style="width: 360px"
            placeholder="请输入不通过原因"
          ></el-input>
        </el-form-item>
        <div>
          <!-- <el-form-item label="授信单号" prop="quotaCode">
            <el-input
              v-model="ruleForm.quotaCode"
              style="width: 50%"
              placeholder="授信单号"
            ></el-input>
          </el-form-item> -->
          <el-form-item label="授信金额" prop="actualAmt">
            <el-input
              v-model="ruleForm.actualAmt"
              style="width: 360px"
              placeholder="请输入授信金额"
              @blur="changeInput"
              type="number"
            ></el-input>
            元
          </el-form-item>
          <el-form-item label="单笔使用比例" prop="orderAmountRate" v-if="ruleForm.applyType == 3 || ruleForm.applyType == 5">
            <el-input
              v-model="ruleForm.orderAmountRate"
              placeholder="请输入单笔使用比例"
              style="width: 360px"
              max="100"
              type="number"
            ></el-input>
            %
          </el-form-item>
          <el-form-item label="授信期限" prop="bankTerm">
            <!-- <el-input
              type="number"
              v-model="ruleForm.bankTerm"
              style="width: 50%"
              placeholder="请输入授信期限"
            ></el-input> -->
            <el-select v-model="ruleForm.bankTerm" clearable
              style="width: 360px" @change="handelChangeRangeDate">
            <el-option
              v-for="dict in dict.type.nlbao_bank_time"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
          </el-form-item>
          <el-form-item label="利率(年化）" prop="quotaRate">
            <el-input
              v-model="ruleForm.quotaRate"
              style="width: 360px"
              placeholder="请输入利率"
              type="number"
             @blur="changeInputRate(ruleForm.quotaRate)"
            ></el-input>
            %
          </el-form-item>
          <el-form-item label="授信起始日期" prop="dataTime">
            <!-- <el-date-picker
              v-model="dataTime"
              style="width: 240px"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :picker-options="isDisabled"
            >
            </el-date-picker> -->
            <el-date-picker
              v-model="dataTime"
              type="date"
              style="width: 360px"
              value-format="yyyy-MM-dd"
              :picker-options="isDisabled"
              @change="handelChangeDate"
              placeholder="选择日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="日期范围">
             <div>{{dataTime}} - {{endDate}}</div>           
          </el-form-item>


          <el-form-item label="授信凭证">
            
            <div class="images" style="display:flex">
                <div class="view-images">
                  <div v-for="(item,index) in quotaPic" :key="index">
                    <div class="view-images-list">
                      <div class="delete-img" @click="deletequotaPic(index, quotaPic)"><i class="el-icon-error"></i></div>
                      <img style="width:150px; height:150px;margin-right:10px" :src="item" alt="" @click="showertificatePic(item)">
                    </div>
                  </div>
                 <!-- Flow -->
                 </div>  
                     <el-upload
                        :action="uploadImgUrl"
                        list-type="picture-card"
                        :on-preview="handlePictureCardPreview"
                        :file-list="quotaPic"
                        value-key="img_url"
                        :auto-upload="true"
                        :show-file-list="false"
                        :on-success="handlSuccessCreditPic"
                        :headers="headers"
						>
							<i class="el-icon-plus"></i>
						</el-upload>
              </div>         
          </el-form-item>
         <!-- <div class="user_card">
            <span class="user_title">授信凭证</span>
        </div> -->
          <!-- <el-form-item label="银行地址">
            <el-input placeholder="请输入银行地址" v-model="ruleForm.bankAddress" style="width: 50%"></el-input>
          </el-form-item>
          <el-form-item label="银行电话">
            <div>
              {{bankPhone}}
            </div>
          </el-form-item>
          <el-form-item label="信贷员" prop="bankUser">
            <el-input
              v-model="input"
              style="width: 50%"
              placeholder="请输入信贷员"
              disabled
            ></el-input>
          </el-form-item> -->
        </div>
        <div v-if="false">
          <el-form-item label="使用【额度支付】购买时："></el-form-item>
          <el-form-item label="是否收取买方费用">
            <el-radio-group
              v-model="isCharge"
              @change="changeIsCharge"
            >
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="服务费分佣设置"></el-form-item>
           <el-row>
            <el-col :span="12">
              <el-form-item label="活畜按头">
                <el-input style="width: 80%" type='number' :disabled='!isCharge' v-model="feeValue" placeholder="请输入佣金"
                ></el-input>元/头
              </el-form-item>
            </el-col>
           </el-row>
          <el-row>
            <el-col :span="6">
              <el-form-item label="平台佣金 ">
                <el-input style="width: 70%" type='number' :disabled='!isCharge' v-model="operationCenterValue" placeholder="请输入佣金"
                ></el-input>元/头
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="省级运营中心佣金 ">
                <el-input style="width: 70%" type='number' :disabled='!isCharge' v-model="platformValue" placeholder="请输入佣金"
                ></el-input>元/头
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="区域合伙人佣金">
                <el-input style="width: 70%" type='number' :disabled='!isCharge' v-model="partnerValue" placeholder="请输入佣金"
                ></el-input>元/头
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="畜牧科技师佣金">
                <el-input style="width: 70%" type='number' :disabled='!isCharge' v-model="pastorValue" placeholder="请输入佣金"
                ></el-input>元/头
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="">平台佣金 + 省级运营中心佣金 + 区域合伙人佣金 + 畜牧科技师佣金 = 每头活畜的佣金</el-form-item>
            <el-row>
              <el-col :span="12">
                <el-form-item label="商城按百分比">
                  <el-input style="width: 80%" type='number' :disabled='!isCharge' v-model="feeValue1" placeholder="请输入百分比"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          <el-row>
            <el-col :span="6">
              <el-form-item label="平台佣金 ">
                <el-input style="width: 70%" type='number' :disabled='!isCharge' v-model="platformValue1" placeholder="请输入百分比"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="省级运营中心佣金 ">
                <el-input style="width: 70%" type='number' :disabled='!isCharge' v-model="operationCenterValue1" placeholder="请输入百分比"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="区域合伙人佣金">
                <el-input style="width: 70%" type='number' :disabled='!isCharge' v-model="partnerValue1" placeholder="请输入百分比"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="畜牧科技师佣金">
                <el-input style="width: 70%" type='number' :disabled='!isCharge' v-model="pastorValue1" placeholder="请输入百分比"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="">平台佣金百分比 + 省级运营中心佣金百分比 + 区域合伙人佣金百分比 + 畜牧科技师佣金百分比 = 100%</el-form-item>
        </div>
        <el-form-item>
          <el-button type="primary" @click="handelSubmit">提交</el-button>
        </el-form-item>
      </el-form>
      <div class="is_show" @click="handelShow" v-if="type == 0">
        {{ showTitle }}
        <i :class="icon"></i>
      </div>
      <div style="display: none">
        <el-button @click="handelEdit" v-if="!disabled">编辑</el-button>
        <!-- <el-button type="primary" @click="handelSubmit" v-if="disabled">保存</el-button> -->
        <el-button @click="cancel" v-if="disabled">取消</el-button>
      </div>

      <div v-show="isShow">
        <el-descriptions title="推荐企业信息" border>
          <el-descriptions-item label="推荐企业名称">{{
            detailInfo.companyName
          }}</el-descriptions-item>
          <el-descriptions-item label="法人">{{
            detailInfo.corprateName
          }}</el-descriptions-item>
          <el-descriptions-item label="企业代码">{{
            detailInfo.certNo
          }}</el-descriptions-item>
        </el-descriptions>

        <el-descriptions title="申请信息" border>
          <el-descriptions-item label="申请人姓名">{{
            detailInfo.userName
          }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{
            detailInfo.userPhone
          }}</el-descriptions-item>
          <el-descriptions-item label="身份证号">{{
            detailInfo.userIdNo
          }}</el-descriptions-item>
          <el-descriptions-item label="申请额度">{{
            detailInfo.applyAmt
          }}</el-descriptions-item>
          <el-descriptions-item label="授信期限">
            <span v-if="detailInfo.quotaTerm"> {{detailInfo.quotaTerm}} 个月</span>
          </el-descriptions-item>
          <el-descriptions-item label="资金用途">{{
            detailInfo.quotaPurpose
          }}</el-descriptions-item>
          <el-descriptions-item label="申请时间">{{
            detailInfo.createTime
          }}</el-descriptions-item>
        </el-descriptions>

        <div class="user_info">
          <el-descriptions title="身份信息" border>
            <el-descriptions-item label="婚姻状况">{{detailInfo.maritalStatus | formartSex}}
            </el-descriptions-item>
            <el-descriptions-item label="配偶姓名">{{
              detailInfo.spouseName
            }}</el-descriptions-item>
            <el-descriptions-item label="配偶身份证号">{{
              detailInfo.spouseIdNo
            }}</el-descriptions-item>
            <!-- <el-descriptions-item v-if="detailInfo.maritalStatus != 1" label="是否有子女">{{
              detailInfo.childFlag == 1 ? '是' : '否'
            }}</el-descriptions-item>
            <el-descriptions-item v-if="detailInfo.maritalStatus != 1" label="子女情况">{{
              handelChildSituation(childSituation, detailInfo.childInfo)
            }}</el-descriptions-item> -->
          </el-descriptions>

          <!-- <div class="user_card">
            <span class="user_title">申请人身份证信息</span>
            <div class="images">
              <div class="view-images">
                <div
                  class="view-images-list"
                  v-for="(imgItem, index) in faImageList"
                  :key="index"
                >
                  <img :src="imgItem.url" alt="" @click="showimage(imgItem)" />
                </div>
              </div>
            </div>
          </div> -->
          <!-- <div class="user_card"> -->
            <!-- <span class="user_title">配偶身份证信息</span> -->
           <!--  <div class="images">
              <div class="view-images">
                <div
                  class="view-images-list"
                  v-for="(imgItem, index) in wifeList"
                  :key="index"
                >
                  <div
                    class="delete-img"
                    v-if="disabled"
                    @click="deleteWifeImg(index, wifeList)"
                  >
                    <i class="el-icon-error"></i>
                  </div>
                  <img
                    :src="imgItem.url"
                    alt=""
                    @click="showWifeImage(imgItem)"
                  />
                </div>
              </div>
            </div> -->
          <!-- </div> -->
        </div>

        <!-- <el-descriptions title="征信信息" border direction="vertical">
          <el-descriptions-item label="申请人征信信息" :span="3">
            <div class="images" style="display: flex">
              <div class="view-images">
                <div
                  v-for="(item, index) in detailInfo.quotaFileList"
                  :key="item.id"
                >
                  <div class="view-images-list" v-if="item.fileType == 1">
                    <div
                      class="delete-img"
                      v-if="commonFlay"
                      @click="deleteApply(index, detailInfo.quotaFileList)"
                    >
                      <i class="el-icon-error"></i>
                    </div>
                    <img
                      v-if="item.fileType == 1"
                      style="width: 150px; height: 150px; margin-right: 10px"
                      :src="item.filePath"
                      alt=""
                      @click="showApplyImage(item)"
                    />
                  </div>
                </div>
              </div>
            </div>
            <el-dialog :visible.sync="dialogVisible">
              <img width="100%" :src="dialogImageUrl" alt="" />
            </el-dialog>
          </el-descriptions-item>
          <el-descriptions-item label="配偶征信信息" :span="3">
            <div class="images" style="display: flex">
              <div class="view-images">
                <div
                  v-for="(item, index) in detailInfo.quotaFileList"
                  :key="item.id"
                >
                  <div class="view-images-list" v-if="item.fileType == 2">
                    <div
                      class="delete-img"
                      v-if="commonFlay"
                      @click="deleteApplyWife(index, detailInfo.quotaFileList)"
                    >
                      <i class="el-icon-error"></i>
                    </div>
                    <img
                      v-if="item.fileType == 2"
                      style="width: 150px; height: 150px; margin-right: 10px"
                      :src="item.filePath"
                      alt=""
                      @click="showApplyImageWife(item)"
                    />
                  </div>
                </div>
              </div>
            </div>
          </el-descriptions-item>
        </el-descriptions> -->

        <!-- <el-descriptions title="流水信息" border direction="vertical">
          <el-descriptions-item label="申请人近12个月银行流水记录" :span="3">
            <div class="images" style="display: flex">
              <div class="view-images">
                <div
                  v-for="(item, index) in detailInfo.quotaFileList"
                  :key="item.id"
                >
                  <div class="view-images-list" v-if="item.fileType == 3">
                    <div
                      class="delete-img"
                      v-if="commonFlay"
                      @click="deleteFlow(index, detailInfo.quotaFileList)"
                    >
                      <i class="el-icon-error"></i>
                    </div>
                    <img
                      v-if="item.fileType == 3"
                      style="width: 150px; height: 150px; margin-right: 10px"
                      :src="item.filePath"
                      alt=""
                      @click="showApplyFlow(item)"
                    />
                  </div>
                </div>
              </div>
            </div>
          </el-descriptions-item>
        </el-descriptions> -->

        <el-descriptions title="订单信息" border v-if="detailInfo.orderInfo">
          <el-descriptions-item label="收款方全称">{{
            detailInfo.companyName
          }}</el-descriptions-item>
          <el-descriptions-item label="订单编号">{{
            detailInfo.userPhone
          }}</el-descriptions-item>
          <el-descriptions-item label="支付方式">预付款 + 尾款</el-descriptions-item>
          <el-descriptions-item label="订单金额（元）">{{
            detailInfo.applyAmt
          }}</el-descriptions-item>
          <el-descriptions-item label="下单时间">{{
            detailInfo.createTime
          }}</el-descriptions-item>
        </el-descriptions>
      <el-table :data="dataOrderInfoList" v-if="detailInfo.orderInfo" highlight-current-row >
        <el-table-column align="center" label="商品名称" prop="goodsName" fixed />
        <el-table-column align="center" label="单价" prop="singlePrice" fixed />
        <el-table-column align="center" label="数量" prop="goodsNum" fixed />
      </el-table>
        <div class="user_info">
          <el-descriptions title="经营信息" border :column="3" :contentStyle="CS">
            <el-descriptions-item label="经营场所地址">
              {{detailInfo.businessPremisesName}} {{detailInfo.address}}
              </el-descriptions-item>
              <!-- <el-descriptions-item label="养殖数量">
              {{detailInfo.numOfBreeding}}
              </el-descriptions-item> -->
             <el-descriptions-item label="牛数量（头）">{{detailInfo.cattleNum}}</el-descriptions-item>
            <el-descriptions-item label="羊数量（只）">{{detailInfo.sheepNum}}</el-descriptions-item>
            <!-- <el-descriptions-item label="其它活畜数量">{{detailInfo.otherNum}}</el-descriptions-item> -->
            <el-descriptions-item label="自有草场（亩）">{{detailInfo.ownPasture}}</el-descriptions-item>
            <el-descriptions-item label="租用草场（亩）">{{detailInfo.rentPasture}}</el-descriptions-item>
            <!-- <el-descriptions-item label="棚圈（间）">{{detailInfo.shedNum}}</el-descriptions-item>
            <el-descriptions-item label="棚圈总价值（万）">{{detailInfo.shedAmt}}</el-descriptions-item>
            <el-descriptions-item label="房屋（平米）">{{detailInfo.housesArea}}</el-descriptions-item>
            <el-descriptions-item label="房屋价值（万）">{{detailInfo.housesAmt}}</el-descriptions-item>
            <el-descriptions-item label="生产机械（台）">{{detailInfo.prodMachinery}}</el-descriptions-item>
            <el-descriptions-item label="生产机械总价值（万）">{{detailInfo.prodMachineryAmt}}</el-descriptions-item>
            <el-descriptions-item label="其它财产信息">{{detailInfo.otherPropertyInfo}}</el-descriptions-item>
            <el-descriptions-item label="其它财产信息总价值 （万）">{{detailInfo.otherPropertyInfoAmt}}</el-descriptions-item> -->

            <el-descriptions-item label="预计所需饲料（吨）">{{detailInfo.fodderTon}}</el-descriptions-item>
            <el-descriptions-item label="预计所需饲草（吨）">{{detailInfo.forageTon}}</el-descriptions-item>
          </el-descriptions>
          <div class="user_card">
            <span class="user_title">棚圈照片</span>
            <div class="images" style="display: flex">
              <div class="view-images">
                <div
                  v-for="(item, index) in shedPic"
                  :key="index"
                >
                  <div class="view-images-list">
                    <div
                      class="delete-img"
                      v-if="commonFlay"
                      @click="deleteAddress(index, shedPic)"
                    >
                      <i class="el-icon-error"></i>
                    </div>
                    <img
                      style="width: 150px; height: 150px; margin-right: 10px"
                      :src="item"
                      alt=""
                      @click="showApplyAddress(item)"
                    />
                  </div>
                </div>
                <!-- Flow -->
              </div>
            </div>
          </div>
          <div class="user_card">
            <span class="user_title">养殖活畜照片</span>
            <div class="images" style="display: flex">
              <div class="view-images">
                <div
                  v-for="(item, index) in livestockPic"
                  :key="index"
                >
                  <div class="view-images-list">
                    <div
                      class="delete-img"
                      v-if="commonFlay"
                      @click="deleteAnimal(index, livestockPic)"
                    >
                      <i class="el-icon-error"></i>
                    </div>
                    <img
                     
                      style="width: 150px; height: 150px; margin-right: 10px"
                      :src="item"
                      alt=""
                      @click="showApplyAnimal(item)"
                    />
                  </div>
                </div>
                <!-- Flow -->
              </div>
            </div>
          </div>
          <div class="user_card">
            <span class="user_title">申请人房屋照片</span>
            <div class="images" style="display:flex">
                <div class="view-images">
                  <div v-for="(item, index) in housesPic" :key="index">
                    <div class="view-images-list">
                      <img style="width:150px; height:150px;margin-right:10px" :src="item" alt="" @click="showApplyHouse(item)">
                    </div>
                  </div>
                 </div>  
              </div>
          </div>
          <div class="user_card">
            <span class="user_title">征信照片</span>
            <div class="images" style="display:flex">
                <div class="view-images">
                  <div v-for="(item, index) in creditPic" :key="index">
                    <div class="view-images-list">
                      <img style="width:150px; height:150px;margin-right:10px" :src="item" alt="" @click="showApplyOther(item)">
                    </div>
                  </div>
                 </div>  
              </div>
          </div>
          <div class="user_card">
            <span class="user_title">其它照片</span>
            <div class="images" style="display:flex">
                <div class="view-images">
                  <div v-for="(item, index) in otherPic" :key="index">
                    <div class="view-images-list">
                      <img style="width:150px; height:150px;margin-right:10px" :src="item" alt="" @click="showApplyOther(item)">
                    </div>
                  </div>
                 </div>  
              </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import baseInfo from "../components/baseInfo/index.vue"
import { queryApplyDetail, changeApproStatus, payFQuotaGuarantorQueryPage } from "@/api/nlbao/guarantee"
import { basicPath2 } from "@/api/base.js"
import { getToken } from "@/utils/auth"
import axios from "axios"

export default {
  dicts: ["nlbao_bank_time"],
  components: {
    baseInfo
  },
  data() {
    return {
      dataTime: '',
      input: "信贷员",
      isShow: false,
      showTitle: "点击查看申请详情",
      icon: "el-icon-arrow-down",
      ruleForm: {
        actualAmt: null,
        bankAddress: "",
        quotaRate: '',
        guarId: '',
        type: '',
        applyType: '',
        orderAmountRate: ''
      },
      rules: {
        approvalStatus: [
          { required: true, message: "请选择审核意见", trigger: "change" }
        ],
        bankAmt: [
          { required: true, message: "请输入评估额度", trigger: "blur" }
        ],
        actualAmt: [
          { required: true, message: "请输入授信金额", trigger: "blur" }
        ],
        // quotaCode:[
        //   { required: true, message: "请输入授信单号", trigger: "blur" }
        // ],
        bankTerm: [
          { required: true, message: "请选择授信期限", trigger: "change" }
        ],
        quotaRate: [
          { required: true, message: "请输入利率", trigger: "blur" },
          {pattern: /(?:^[1-9]([0-9]+)?(?:\.[0-9]{1,2})?$)|(?:^(?:0)$)|(?:^[0-9]\.[0-9](?:[0-9])?$)/, message: '利率小数点不能超过两位', trigger: 'blur'}
        ],
        orderAmountRate: [
          { required: true, message: '请输入使用比例', trigger: 'blur' },
          {pattern: /(?:^[1-9]([0-9]+)?(?:\.[0-9]{1,2})?$)|(?:^(?:0)$)|(?:^[0-9]\.[0-9](?:[0-9])?$)/, message: '比例小数点不能超过两位', trigger: 'blur'}
        ],
        dataTime: [
          // { type: 'date', required: true, message: '请选择授信起始日期', trigger: 'change' }
        ]
      },
      uploadImgUrl:
        process.env.VUE_APP_BASE_API + `${basicPath2}files/obs/fileUpload`, // 上传的图片服务器地址
      headers: {
        Authorization: getToken()
      },
      orderId: "",
      // active: undefined,
      active: undefined,
      orderInfo: {},
      orderData: [],
      id: "",
      detailInfo: {},
      dialogVisible: false,
      dialogImageUrl: "",
      hideUpload: true,
      // 法人身份证信息
      faImageList: [],
      envflag: false,
      maxCount: 2, //证件
      // 配偶身份证信息
      wifeList: [],
      wifeFlag: false,
      wifeCount: 2,
      // 申请人征信
      quotaFileList: [],
      commonFlay: false,
      imgUrl: "",
      disabled: false,
      marketAddressid: [],
      addressOptions: [],
      province: "",
      city: "",
      area: "",
      detailAdd: "",
      armtList: [],
      minArmt: undefined,
      bankPhone:'',
      type: "",
      status: false,
      currentUserInfo: {},
      currentUserId: "",
      isDisabled: {
        // disabledDate: (time) =>{
        //   // 最大日期为当前日期
        //   let myDate = new Date();
        //   let maxDay = myDate.setDate(new Date().getDate() - 1);    
        //   return time.getTime() <= maxDay;
        // }
      },
      endDate: '',
      dateRange: '',
      CS: {
        width: "200px", //最小宽度
        "word-break": "break-all", //过长时自动换行
      },
      isCharge: 1,
      feeValue: '',
      partnerValue: '',
      pastorValue: '',
      platformValue: '',
      operationCenterValue: '',
      feeValue1: '',
      partnerValue1: '',
      pastorValue1: '',
      platformValue1: '',
      operationCenterValue1: '',
      shedPic: [],
      livestockPic: [],
      housesPic: [],
      otherPic: [],
      creditPic: [],
      guarantorList: [],
      quotaPic: [],
      dataOrderInfoList: [],
      childSituation: [
          { name: '上学', value: 1 },
          { name: '私企上班', value: 2 },
          { name: '公职人员', value: 3 },
          { name: '牧业', value: 4 },
          { name: '个体', value: 5 },
          { name: '无业', value: 6 }
      ]
    }
  },
   filters: {            
    // 状态 (0取消 1提交成功，2畜牧师成功，3运营人成功，4推荐企业成功，5银行成功)
    formartSex: (type) =>{
      const statusMap = {
        1: '未婚',
        2: '已婚',
        3: '离异',
        4: '丧偶',
      }
      return statusMap[type]
    },
    handelChildSituation() {
      return (list, value) => {
        let name = "";
        list.forEach((item) => {
          if (item.value == value) {
            name = item.name;
          }
        });
        return name;
      };
    },
  },
  created() {
    this.getAddressList()
    this.id = this.$route.query.id
    console.log(this.id)
    this.getQueryApplyDetail()
    this.type = this.$route.query.type
    this.type == 1 ? (this.isShow = true) : (this.isShow = false)
    const user_info = JSON.parse(window.localStorage.getItem("extsysUserInfo"))
    this.currentUserInfo = user_info
    this.getGuarantorList()
  },
  mounted() {},
  methods: {
    changeInputRate(val){
      const rateVal = Number(val);
       if(rateVal > 100) {
         this.$message.error('利率不能超过100')
       }
    },
    handelChangeDate() {
      console.log(this.dataTime);
      if(this.ruleForm.bankTerm) {
        this.bankEndTime(this.dataTime, Number(this.ruleForm.bankTerm))
      }
    },

    getGuarantorList() {
        payFQuotaGuarantorQueryPage({
                pageNum: 1,
                pageSize: 1000
            }).then((res) => {
          if (res.stautscode == 200) {
                this.guarantorList = res.data.list
                // if (this.guarId) {
                //     this.guarantorList.forEach(item => {
                //         if(this.guarId == item.id) {
                //             this.currentCompany = item;
                //         }
                //     })
                // }
          }
        })
    },
    handelChangeRangeDate(){
      if(this.dataTime){
        this.bankEndTime(this.dataTime, Number(this.ruleForm.bankTerm))
      }
    },
    // 审核详情展示
    handelChangeStatus() {
      console.log(this.ruleForm.approvalStatus)
      this.ruleForm.approvalStatus == 7
        ? (this.status = true)
        : (this.status = false)
    },
    // 获取地址信息
    async getAddressList() {
      const { data: res } = await axios.get("/city.json")
      this.addressOptions = res
    },
    async getQueryApplyDetail() {
      const { data: res } = await queryApplyDetail({ quotaId: this.id })
      this.detailInfo = res
      this.active = res.approvalStatus
      this.shedPic = this.detailInfo.shedPic && this.detailInfo.shedPic.split(',');
      this.livestockPic = this.detailInfo.livestockPic && this.detailInfo.livestockPic.split(',');
      this.housesPic = this.detailInfo.housesPic && this.detailInfo.housesPic.split(',');
      this.otherPic = this.detailInfo.otherPic && this.detailInfo.otherPic.split(',');
      this.creditPic = this.detailInfo.creditPic && this.detailInfo.creditPic.split(',');
      this.quotaPic = this.detailInfo.quotaPic && this.detailInfo.quotaPic.split(',') || [];
      if (this.detailInfo.orderInfo) {
        this.dataOrderInfoList = JSON.parse(this.detailInfo.orderInfo).goodsModel
      }
      // applyAmt 申请金额
      // platformAmt  平台金额
      // bankAmt   银行金额
      // guarAmt 推荐企业
      this.armtList.push(
        res.applyAmt,
        res.platformAmt,
        res.actualAmt,
        // res.bankAmt,
      )

      // if (res.guarId) {
      //   this.armtList.push(res.guarAmt)
      // }
      this.ruleForm.orderAmountRate = res.orderAmountRate
      this.ruleForm.type = res.type;
      this.ruleForm.applyType = res.applyType;
      let addressArr = res.businessPremises.split(",")
      this.marketAddressid = addressArr
      const bankAddress = res?.bankAddress? res?.bankAddress : ''
      this.ruleForm.bankAddress = bankAddress;
      this.bankPhone = res.bankContactPhone
      const minArmt = this.armtList.reduce((x, y) => x < y ? x : y)
      this.ruleForm.actualAmt = minArmt
      // this.minArmt = minArmt
      this.ruleForm.bankTerm = res.bankTerm.toString();
      this.ruleForm.quotaRate = res.quotaRate;
      this.$set(this.ruleForm,"dataTime",[res.quotaStartTime]);
      // <div>{{dataTime}} - {{endDate}}</div>
      this.dataTime = res.quotaStartTime;
      this.endDate = res.quotaEndTime
      this.$forceUpdate()

      this.faImageList.push({
        url: this.detailInfo.userIdPic1,
        url: this.detailInfo.userIdPic2
      })
      this.faImageList.push({
        url: this.detailInfo.userIdPic2
      })
      this.wifeList.push({
        url: this.detailInfo.spouseIdPic1
      })
      this.wifeList.push({
        url: this.detailInfo.spouseIdPic2
      })
      console.log(this.faImageList)
    },
    // 申请人及配偶身份证上传
    deleteimg(index, fileList) {
      this.$nextTick(() => {
        fileList.splice(index, 1)
        this.faImageList = fileList
        this.envflag = !(fileList.length >= this.maxCount)
      })
    },
    showimage(file) {
      console.log(file)
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    handlEnvironmentSuccess(res, file, fileList) {
      this.faImageList.push({
        url: res.result[0].objectUrl
      })
      this.$nextTick(() => {
        this.envflag = !(fileList.length >= this.maxCount)
      })
    },
    deleteWifeImg(index, fileList) {
      fileList.splice(index, 1)
      this.wifeList = fileList
      this.$nextTick(() => {
        this.wifeFlag = !(fileList.length >= this.maxCount)
      })
    },
    showWifeImage(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    handlWifeSuccess(res, file, fileList) {
      this.wifeList.push({
        url: res.result[0].objectUrl
      })
      this.$nextTick(() => {
        this.wifeFlag = !(fileList.length >= this.maxCount)
      })
    },
    handlSuccessCreditPic(res, file, fileList) {
      this.quotaPic.push(file.response.result[0].objectUrl)
    },
    deletequotaPic(index, fileList) {
      fileList.splice(index, 1)
      this.quotaPic = fileList
    },
    handlePictureCardPreview() {},
    // 申请人征信上传
    deleteApply(index, fileList) {
      console.log(index)
      console.log(fileList)
      fileList.splice(index, 1)
      this.detailInfo.quotaFileList = fileList
    },
    showApplyImage(file) {
      console.log(file)
      this.dialogImageUrl = file.filePath
      this.dialogVisible = true
    },
    handlSuccessApply(res, file, fileList) {
      this.detailInfo.quotaFileList.push({
        fileType: 1,
        fileName: file.url,
        filePath: res.result[0].objectUrl
      })
    },
    // 配偶征信信息
    deleteApplyWife(index, fileList) {
      console.log(index)
      console.log(fileList)
      fileList.splice(index, 1)
      this.detailInfo.quotaFileList = fileList
    },
    showApplyImageWife(file) {
      console.log(file)
      this.dialogImageUrl = file.filePath
      this.dialogVisible = true
    },
    showApplyHouse(file) {
      this.dialogImageUrl = file
      this.dialogVisible = true
    },
    showertificatePic(file) {
      this.dialogImageUrl = file
      this.dialogVisible = true
    },
    showApplyOther(file) {
      this.dialogImageUrl = file
      this.dialogVisible = true
    },
    handlSuccessApplyWife(res, file, fileList) {
      this.detailInfo.quotaFileList.push({
        fileType: 2,
        fileName: file.url,
        filePath: res.result[0].objectUrl
      })
    },
    // 申请人近12个月银行流水记录  
      deleteFlow(index, fileList) {
      console.log(index)
      console.log(fileList)
      fileList.splice(index, 1)
      this.detailInfo.quotaFileList = fileList
    },
    showApplyFlow(file) {
      console.log(file)
      this.dialogImageUrl = file.filePath
      this.dialogVisible = true
    },
    handlSuccessFlow(res, file, fileList) {
      this.detailInfo.quotaFileList.push({
        fileType: 3,
        fileName: file.url,
        filePath: res.result[0].objectUrl
      })
    },
    // 经营场所场地照片
    deleteAddress(index, fileList) {
      fileList.splice(index, 1)
      this.detailInfo.quotaFileList = fileList
    },
    showApplyAddress(file) {
      this.dialogImageUrl = file
      this.dialogVisible = true
    },
    handlSuccessAddress(res, file, fileList) {
      this.detailInfo.quotaFileList.push({
        fileType: 4,
        fileName: file.url,
        filePath: res.result[0].objectUrl
      })
    },
    // 养殖活畜照片
    deleteAnimal(index, fileList) {
      fileList.splice(index, 1)
      this.detailInfo.quotaFileList = fileList
    },
    showApplyAnimal(file) {
      this.dialogImageUrl = file
      this.dialogVisible = true
    },
    handlSuccessAnimal(res, file, fileList) {
      this.detailInfo.quotaFileList.push({
        fileType: 5,
        fileName: file.url,
        filePath: res.result[0].objectUrl
      })
    },
    cancel() {
      this.commonFlay = false
      this.disabled = false
      this.envflag = false
      this.wifeFlag = false
    },
    changeInput() {
      if (Number(this.minArmt) < Number(this.ruleForm.actualAmt)) {
        this.$message.info("授信金额不能超过最低金额")
      }
    },
    bankEndTime(date, month) {
      const day = new Date(date)
        day.setMonth(day.getMonth() + month);
        day.setDate(day.getDate() - 1);
        day.toLocaleDateString()
        let y = day.getFullYear()
        let m = day.getMonth() + 1
        m = m < 10 ? ('0' + m) : m
        let d = day.getDate()
        d = d < 10 ? ('0' + d) : d
        const time = y + '-' + m + '-' + d;
        this.endDate = time
    },
    changeIsCharge() {
      if (!this.isCharge) {
        this.feeValue = 0;
        this.partnerValue = 0;
        this.pastorValue = 0;
        this.platformValue = 0;
        this.operationCenterValue = 0;
        this.feeValue1 = 0;
        this.partnerValue1 = 0;
        this.pastorValue1 = 0;
        this.platformValue1 = 0;
        this.operationCenterValue1 = 0;
      }

    },
    handelSubmit() {
      this.$refs.ruleForm.validate((valid) => {
          if (valid) {
            // if (this.status) {
              //   const totalLoan = Number(this.partnerValue) + Number(this.pastorValue) + Number(this.platformValue) + Number(this.operationCenterValue)
              //   const totalLoan1 = Number(this.partnerValue1) + Number(this.pastorValue1) + Number(this.platformValue1) + Number(this.operationCenterValue1)
              // if (Number(this.minArmt) < Number(this.ruleForm.actualAmt)) {
              //   this.$message.info("授信金额不能超过最低金额")
              // } else if(Number(this.ruleForm.quotaRate)>100) {
              //   this.$message.error('利率不能超过100')
              // } else if (this.detailInfo.approvalStatus == 5 && totalLoan > this.feeValue && this.isCharge) {
              //   this.$message.error('合伙人佣金 + 畜牧师佣金不能大于每头活畜的佣金')
              // } else if (this.detailInfo.approvalStatus == 5 && totalLoan1 != 100 && this.isCharge) {
              //   this.$message.error('合伙人佣金百分比 + 畜牧师佣金百分比必须等于百分百')
              // } else {
                const quotaStartTime = this.dataTime ? this.dataTime : ""
                const quotaEndTime = this.endDate ? this.endDate : ""
                
                const passParams = {
                  id: Number(this.id),
                  // ...this.ruleForm,
                  actualAmt: this.ruleForm.actualAmt,
                  approvalStatus: 7,
                  quotaCode: this.ruleForm.quotaCode,
                  bankTerm: this.ruleForm.bankTerm,
                  quotaRate: this.ruleForm.quotaRate,
                  bankUserId: this.currentUserInfo.access_token,
                  quotaStartTime: quotaStartTime,
                  quotaEndTime: quotaEndTime,
                  // bankAmt: this.detailInfo.bankAmt,
                  guarId: this.ruleForm.guarId,
                  type: this.ruleForm.type,
                  applyType: this.ruleForm.type == 1 ? '4' : this.ruleForm.applyType,
                  quotaPic: this.quotaPic && this.quotaPic.join(',') || '',
                  orderAmountRate: this.ruleForm.orderAmountRate
                }
                // let quotaFeeList = []
                // if(this.detailInfo.approvalStatus == 5) {
                //   const item1 = {
                //     quotaId: Number(this.id),
                //     feeType: 1,
                //     feeValue: this.feeValue,
                //     partnerValue: this.partnerValue,
                //     pastorValue: this.pastorValue,
                //     platformValue: this.platformValue,
                //     operationCenterValue: this.operationCenterValue,
                //   }
                //   const item2 = {
                //     quotaId: Number(this.id),
                //     feeType: 2,
                //     feeValue: this.feeValue1,
                //     partnerValue: this.partnerValue1,
                //     pastorValue: this.pastorValue1,
                //     platformValue: this.platformValue1,
                //     operationCenterValue: this.operationCenterValue1,
                //   }
                //   quotaFeeList.push(item1)
                //   quotaFeeList.push(item2)
                // }
                // passParams.quotaFeeList = quotaFeeList
                // return
                changeApproStatus(passParams).then((res) => {
                  if (res.stautscode == 200) {
                    this.$message.success("放款成功")
                    this.commonFlay = false
                    this.disabled = false
                    this.envflag = false
                    this.wifeFlag = false
                    this.$router.go(-1)
                  } else{
                    this.$message.error(res.msg);
                  }
                })
            // } else {
            //   this.$refs.ruleForm.validate((valid) => {
            //     if (valid) {
            //       const params = {
            //         id: Number(this.id),
            //         approvalStatus: this.ruleForm.approvalStatus,
            //         reason: this.ruleForm.reason
            //       }
            //       if (this.ruleForm.approvalStatus === 6) {
            //         if (!this.ruleForm.reason) {
            //           this.$message.error("审核意见不能为空")
            //         } else {
            //           changeApproStatus(params).then((res) => {
            //             if (res.stautscode == 200) {
            //               this.$message.success("驳回成功")
            //               this.commonFlay = false
            //               this.disabled = false
            //               this.envflag = false
            //               this.wifeFlag = false;
            //               this.$router.go(-1)
            //             } else {
            //               this.$message.error(res.msg)
            //             }
            //           })
            //         }
            //       }
            //     }
            //   })
            // }
          }
      })
      
    },
    handelShow() {
      this.isShow = !this.isShow
      this.isShow == true
        ? (this.showTitle = "收起申请详情")
        : (this.showTitle = "展开查看申请详情")
      this.isShow == true
        ? (this.icon = "el-icon-arrow-up")
        : (this.icon = "el-icon-arrow-down")
    },

    // 启用/禁用编辑
    handelEdit() {
      this.disabled = true
      this.hideUpload = false
      this.commonFlay = true
    },

    // 三级地址选择
    handleChange(value) {
      console.log(value)
      if (value && value.length != 0) {
        let arr = this.$refs["cascaderAddr"].getCheckedNodes()[0].pathLabels
        console.log(arr)
        this.detailAdd = arr.join(",")
        this.province = arr[0]
        this.city = arr[1]
        this.area = arr[2]
      }
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
::v-deep input[type="number"] {
  -moz-appearance: textfield;
}
.is_show {
  width: 100%;
  text-align: center;
  color: rgb(22, 155, 213);
  cursor: pointer;
}
::v-deep .el-steps--horizontal {
  width: 80%;
}
.step_style {
  width: 100%;
  display: flex;
  justify-content: center;
  margin: 10px 0;
}
::v-deep .el-descriptions__header {
  background-color: rgb(242, 242, 242);
  padding: 0 20px;
  margin: 30px 0 10px 0;
  height: 40px;
}

.user_info {
  .user_card {
    .user_title {
      margin: 10px 0;
      display: inline-block;
      color: rgb(153, 153, 153);
      font-size: 14px;
    }
    .images {
      display: flex;
      flex-wrap: wrap;
      .el-image {
        width: 150px;
        height: 150px;
        margin-right: 20px;
      }
    }
  }
}

.view-images {
  display: flex;
  flex-wrap: wrap;
  .view-images-list {
    width: 146px;
    height: 146px;
    display: inline-block;
    margin: 0 10px 0 10px;
    position: relative;
    .delete-img {
      display: inline-block;
      font-size: 24px;
      cursor: pointer;
      color: rgba(0, 0, 0, 0.5);
      position: absolute;
      top: -16px;
      right: -9px;
    }
    img {
      width: 100%;
      height: 100%;
      border-radius: 10px;
      cursor: pointer;
    }
  }
}
</style>

