 
  <template>
    <div>
       <!-- 消费 -->
       <el-descriptions class="margin-top" title="" :column="3" border :contentStyle="CS" v-if="title==1">
           <el-descriptions-item label="申请人">
             <span>{{ detailInfo.userName}}</span>
            </el-descriptions-item>
           <el-descriptions-item label="联系电话">
             <span>{{ detailInfo.userPhone}}</span>
            </el-descriptions-item>
           <el-descriptions-item label="身份证号">
              <span>{{ detailInfo.userIdNo}}</span>
          </el-descriptions-item>
           <el-descriptions-item label="推荐企业">
              <span>{{ detailInfo.companyName}}</span>
           </el-descriptions-item>
           <el-descriptions-item label="放款银行">
              <span>{{ detailInfo.bankName}}</span>
       </el-descriptions-item>
           <el-descriptions-item label="放款单号">
             <span>{{ detailInfo.quotaCode}}</span>
            </el-descriptions-item>
           <el-descriptions-item label="放款金额">
              <span>{{ detailInfo.actualAmt}}</span>
              </el-descriptions-item>
           <el-descriptions-item label="放款期限">
             <span>
               <span v-if="detailInfo.bankTerm">{{ detailInfo.bankTerm}} 个月</span>
               </span>
             </el-descriptions-item>
           <el-descriptions-item label="利率（年化）">
             <span>{{ detailInfo.quotaRate}}</span>
             </el-descriptions-item>
       </el-descriptions>
       <!-- 还款 -->
        <el-descriptions class="margin-top" title="" :column="3" border :contentStyle="CS" v-else-if="title==0">
           <el-descriptions-item label="申请人姓名">
             <span>{{ detailInfo.userName}}</span>
            </el-descriptions-item>
           <el-descriptions-item label="联系电话">
             <span>{{ detailInfo.userPhone}}</span>
            </el-descriptions-item>
           <el-descriptions-item label="身份证号">
              <span>{{ detailInfo.userIdNo}}</span>
          </el-descriptions-item>
           <el-descriptions-item label="推荐企业">
              <span>{{ detailInfo.companyName}}</span>
           </el-descriptions-item>
           <el-descriptions-item label="放款银行">
              <span>{{ detailInfo.bankName}}</span>
       </el-descriptions-item>
           <el-descriptions-item label="放款单号">
             <span>{{ detailInfo.quotaCode}}</span>
            </el-descriptions-item>
           <el-descriptions-item label="放款金额">
              <span>{{ detailInfo.actualAmt}}</span>
              </el-descriptions-item>
           <el-descriptions-item label="放款期限">
             <span>
               <span v-if="detailInfo.bankTerm">{{ detailInfo.bankTerm}}个月</span></span>
             </el-descriptions-item>
           <el-descriptions-item label="利率（年化）">
             <span>{{ detailInfo.quotaRate}}%</span>
             </el-descriptions-item>
       </el-descriptions>
       <div class="titles">{{labelTitle}}</div>
        <el-table :data="tableData">
            <el-table-column v-for="(item, index) in header" :key="index" :prop="item.prop" :label="item.label">
                <template slot-scope="scope">
                    <!-- 操作列 -->
                    <template v-if="item.type">                     
                        <el-button size="mini" type="text" v-if="upload && scope.row.isShowContract > -1" @click="uploadFn(scope.row)">上传合同</el-button>
                        <!-- <el-button size="mini" type="text" @click="handelOrderDetail(scope.row)">查看详情</el-button> -->
                        <el-button size="mini" type="text" @click="handelOrderDownload(scope.row)">购销合同下载</el-button>
                    </template>
                    <!-- 参数过滤列 -->
                    <template v-else-if="item.prop == 'payType'">
                      <span v-if="scope.row[item.prop]==22">我的额度</span> 
                    </template>
                    <template v-else-if="item.prop == 'status'">
                      <span>{{scope.row[item.prop] | formartOrderStatus}}</span>
                    </template>
                    
                    <!-- 普通列 -->
                    <template v-else>                     
                       {{scope.row[item.prop]}}
                    </template>
                </template>
            </el-table-column>
        </el-table>
         <!-- 订单详情 -->
     <!--  <order-details
        :orderdata="orderdata"
        v-if="orderdata.dialogVisible"
      ></order-details> -->
      <!-- 临时替换方案 -->
       <model-form :fieldData="fieldData" v-if="fieldData.open" @close="close"></model-form>
      <el-dialog
        title="上传合同"
        :visible.sync="uploadFile"
        width="30%"
        :before-close="handleClose">
        <div>
          <file-upload :limit='1' :fileType='["pdf"]' v-model="uploadFileUrl"></file-upload>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="submitUpload">确 定</el-button>
        </span>
      </el-dialog>
    </div>
</template>

<script>
// import orderDetails from "@/views/xmb/feedMall/order/sales/components/orderDetails.vue";
import modelForm from "@/views/xmb/trade/order/animalOrder/components/modelForm.vue";
import {
  queryApplyDetail
} from "@/api/nlbao/guarantee"

import { exportContract, uploadContract } from '@/api/nlbao/repayment'
export default {
name: 'repayinfo',
    data() {
        return {
          dialogVisible: false,
          CS: {
            width: "250px", //最小宽度
            "word-break": "break-all", //过长时自动换行
          },
          title: '',
            // 表格数据
          header: [], // 还款记录表头
          orderdata: {
            dialogVisible: false,
            id: "",
          },
          dataRow: {},
          uploadFile: false,
          uploadFileUrl: '',
          fieldData: {
                open: false,
                disable: false,
          },
          id: '',
          detailInfo: {}
        };
    },
    props: {
      tableData: Array,
      upload: String
    },
    filters: {            
    formartOrderStatus: (type) =>{
      const statusMap = {
        0: '订单生成',
        1: '交易受理',
        2: '支付成功',
        3: '确认收货中',
        4: '已完成',
      }
      return statusMap[type]
    },
  },
    components: {
      // orderDetails
      modelForm
    },
    computed: {
      labelTitle: function(){
        return this.title == 0 ? '还款记录' : '消费记录'
      },
    },
    watch: {
     tableData: (val) => {
        if(val && val.length!=0) {
            val.map((item,index) => {
            item.roleId = index + 1
              return index
          })
         }
     }
    },
    created() {
      //  console.log(this.$route.query);
       this.title = this.$route.query.type;
        this.id = this.$route.query.id
       const header = [
           { prop: 'roleId', label: '序号'},
           { prop: 'repaymentTime', label: '还款时间'},
           { prop: 'repaymentCode', label: '还款订单号'},
           { prop: 'repaymentAmt', label: '还款金额（本金）'},
           { prop: 'repaymentRate', label: '付利息'},
           { prop: 'avlAmt', label: '剩余可用额度（元）'},
       ];
       const payHeader = [
            { prop: 'roleId', label: '序号'},
            { prop: 'orderCode', label: '订单编号'},
            { prop: 'amountYuan', label: '消费金额（元）'},
            { prop: 'transactionTime', label: '交易时间'},
            { prop: 'payType', label: '支付方式'},
            { prop: 'status', label: '订单状态'},
            { prop: '1', label: '操作' , type: 1}
       ]
       this.title == 0 ? this.header = header : this.header = payHeader;
    },
    mounted() {
      this.getQueryApplyDetail()
    },
    methods: {
       close() {
            this.fieldData.open = false;
            this.fieldData.disable = false;
        },
        handelOrderDetail(row){
             /*  this.orderdata.title = "订单详情";
              this.orderdata.id = row.orderItemCode;
              this.$nextTick(() => {
                this.orderdata.dialogVisible = true;
                stringObject.substr(1).substring(-1,0) //就是可行的啦

              }) */
            // const order = row.orderItemCode.substr(1)
            // const orderId = order.substring(0,order.length-1)
            // console.log(row.orderItemCode+'==========>>>')
            // console.log(orderId)
            this.fieldData.open = true;
            this.fieldData.id = row.orderItemCode;
            this.fieldData.title = "订单详情";
            this.fieldData.disable = true;
        },
        handelOrderDownload(row){
          exportContract({orderCode: row.orderCode}).then(res => {
            if(res.stautscode === 200) {
              if(res.data.docs) {
                window.open(res?.data?.docs[0]?.fileUrl, '_blank');
              } else {
                window.open(res.data, '_blank');
              }
            } else {
              this.$message.error('合同下载失败')
            }

          })
        },
        uploadFn(row) {
          this.uploadFile = true;
          this.dataRow = row
        },
        submitUpload(){
          uploadContract({
            orderCode: this.dataRow.orderCode,
            contractPathUrl: this.uploadFileUrl
          }).then(res => {
            if(res.stautscode === 200) {
              this.$message.success(res.msg)
              this.handleClose()
            } else {
              this.$message.error(res.msg)
            }
          })
        },
        handleClose() {
          this.uploadFileUrl = '';
          this.uploadFile = false;
        },
        async getQueryApplyDetail() {
          const { data: res } = await queryApplyDetail({ quotaId: Number(this.id) })
          this.detailInfo = res
        },
    }
};
</script>

<style scoped lang="scss">
.titles{
    margin: 10px 5px;
}
</style> 