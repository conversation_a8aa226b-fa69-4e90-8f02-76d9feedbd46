<template>
  <div class="app-container">
    <div class="table user_info">
      <div class="step_style" v-if="type==1">
      <el-alert
         v-if="detailInfo.approvalStatus == 6"
        :title="detailInfo.approvalStatusName"
        type="warning"
        :description="detailInfo.reason"
        :closable="false">
      </el-alert>
       <el-alert
        v-else
        :title="detailInfo.approvalStatusName"
        type="warning"
        :closable="false">
      </el-alert>
      </div>
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-width="150px"
        class="demo-ruleForm"
        v-if="type == 0"
      >

        <el-form-item label="选择银行" prop="disBank" v-if="currentUserInfoCustomer.level == 2">
          <el-radio-group
            v-model="disBank"
          >
            <el-radio :label="1">本行</el-radio>
            <el-radio :label="2">分配支行</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <div v-if="disBank == 1">
           <el-form-item label="审核意见" prop="approvalStatus">
            <el-radio-group
              v-model="ruleForm.approvalStatus"
              @change="handelChangeStatus"
            >
              <el-radio :label="5">审核通过</el-radio>
              <el-radio :label="6">审核不通过</el-radio>
            <el-radio :label="11">审核驳回并添加申请人至黑名单</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label=""
          prop="reason"
          v-if="ruleForm.approvalStatus == 6 || ruleForm.approvalStatus == 11 "
        >
          <el-select v-model="ruleForm.reason" placeholder="请选择"
              style="width: 360px">
            <el-option
              v-for="item in checkOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label=""
          prop="reason"
          v-if="ruleForm.approvalStatus == 6 || ruleForm.approvalStatus == 11 "
        >
          <el-input
            type="textarea"
            v-model="reasonOther"
            style="width: 360px"
            placeholder="请输入"
            v-if='ruleForm.reason == "其他"'
          ></el-input>
        </el-form-item>
          <el-form-item label="申请人申请额度">
            <div>{{ detailInfo.applyAmt }} 元
              <el-tooltip effect="dark" placement="top">
                <div slot="content" v-html="tooltips"></div>
                <img style="width: 16px;height: 16px; margin-left: 10px;" src="../../../assets/images/mes/info-circle.png" />
              </el-tooltip>
            </div>
          </el-form-item>
          <el-form-item label="平台评估额度">
            <div>{{ detailInfo.platformAmt }}</div>
          </el-form-item>
        <!-- <el-form-item label="推荐企业评估额度" v-if="detailInfo.guarId">
          <div>{{ detailInfo.guarAmt }}</div>
        </el-form-item> -->

        <div v-if="status">
          <!-- <el-form-item label="银行评估额度" prop="bankAmt">
            <el-input
              v-model="ruleForm.bankAmt"
              type='number'
              style="width: 50%"
              oninput="if(value.length > 8) value=value.slice(0, 8)"
              placeholder="请输入评估额度"
            ></el-input>
            元
          </el-form-item> -->

        <el-form-item label="放款方式" prop="disBank">
            <el-select v-model="ruleForm.applyType"
              style="width: 360px">
              <el-option value="1" label="活畜抵押" />
              <el-option value="2" label="信用额度" />
              <el-option v-if="detailInfo.userType != 3" value="3" label="无货质押" />
              <el-option value="4" label="企业推荐" />
              <el-option v-if="detailInfo.userType != 3" value="5" label="仓单质押" />
              <el-option value="6" label="粮仓无货质押" />
              <el-option v-if="detailInfo.userType != 3" value="7" label="粮仓仓单质押" />
          </el-select>
        </el-form-item>
          <el-form-item label="授信金额" prop="actualAmt">
            <el-input
              v-model="ruleForm.actualAmt"
              style="width: 360px"
              oninput="if(value.length > 8) value=value.slice(0, 8)"
              placeholder="请输入授信金额"
              @blur="changeInput"
              type="number"
            ></el-input>
            元
          </el-form-item>
          <el-form-item label="单笔使用比例" prop="orderAmountRate" v-if="ruleForm.applyType == 3 || ruleForm.applyType == 5">
            <el-input
              v-model="ruleForm.orderAmountRate"
              placeholder="请输入单笔使用比例"
              style="width: 360px"
              type="number"
              :max="100"
            ></el-input>
            %
          </el-form-item>
          <el-form-item label="授信期限" prop="bankTerm">
            <!-- <el-input
              type="number"
              v-model="ruleForm.bankTerm"
              style="width: 50%"
              placeholder="请输入授信期限"
            ></el-input> -->
            <el-select v-model="ruleForm.bankTerm"
              style="width: 360px" clearable @change="handelChangeRangeDate">
              <el-option
                v-for="dict in dict.type.nlbao_bank_time"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
          </el-select>
          </el-form-item>
          <el-form-item label="其他授信期限" prop="bankTerm" v-if="isQankTermSelect">
          <el-input
              v-model="otherTime"
              style="width: 360px"
              placeholder="请输入授信期限"
              type="number"
              @blur="changeotherTime"
            ></el-input>
          </el-form-item>
          <el-form-item label="利率(年化）" prop="quotaRate">
            <el-input
              v-model="ruleForm.quotaRate"
              style="width: 360px"
              placeholder="请输入利率"
              type="number"
              max="100"
              @blur="changeInputRate(ruleForm.quotaRate)"
            ></el-input>
            %
          </el-form-item>
          <el-form-item label="授信起始日期" prop="dataTime">
            <!-- <el-date-picker
              v-model="dataTime"
              style="width: 240px"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :picker-options="isDisabled"
            >
            </el-date-picker> -->
            <el-date-picker
              v-model="dataTime"
              style="width: 360px"
              type="date"
              value-format="yyyy-MM-dd"
              :picker-options="isDisabled"
              @change="handelChangeDate"
              placeholder="选择日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="日期范围">
             <div>{{dataTime}} - {{endDate}}</div>           
          </el-form-item>

          <el-form-item label="上传授信凭证">
         <!-- <div class="user_card"> -->
            <!-- <span class="user_title">上传授信凭证</span> -->
            <div class="images" style="display:flex">
                <div class="view-images">
                  <div v-for="(item,index) in certificatePic" :key="item.id">
                    <div class="view-images-list">
                      <div class="delete-img" @click="deleteCertificatePic(index, certificatePic)"><i class="el-icon-error"></i></div>
                      <img style="width:150px; height:150px;margin-right:10px" :src="item" alt="" @click="showertificatePic(item)">
                </div>
                  </div>
                 <!-- Flow -->
                 </div>  
                     <el-upload
                        :action="uploadImgUrl"
                        list-type="picture-card"
                        :on-preview="handlePictureCardPreview"
                        :file-list="certificatePic"
                        value-key="img_url"
                        :auto-upload="true"
                        :show-file-list="false"
                        :on-success="handlSuccessCreditPic"
                        :headers="headers"
						>
							<i class="el-icon-plus"></i>
						</el-upload>
              </div>
        <!-- </div> -->
          </el-form-item>
          <!-- <el-form-item label="银行地址">
            <el-input placeholder="请输入银行地址" v-model="ruleForm.bankAddress" style="width: 50%"></el-input>
          </el-form-item> -->
          <!-- <el-form-item label="银行电话">
            <div>
              {{bankContactPhone}}
            </div>
          </el-form-item> -->
          <!-- <el-form-item label="信贷员" prop="bankUser">
            <el-input
              v-model="input"
              style="width: 50%"
              placeholder="请输入信贷员"
              disabled
            ></el-input>
          </el-form-item> -->
        </div>
        </div>
        <div v-if="disBank == 2">
        <el-form-item label="选择支行" prop="disBank">
            <el-select v-model="ruleForm.bankUserId">
              <el-option
                v-for="dict in subBankList"
                :key="dict.value"
                :label="dict.text"
                :value="dict.value"
              />
          </el-select>
        </el-form-item>
        </div>
        <el-form-item>
          <el-button type="primary" v-if="disBank == 1" @click="handelSubmit">提交</el-button>
          <el-button type="primary" v-if="disBank == 2" @click="handelSubmit1">提交</el-button>
        </el-form-item>
      </el-form>
      <div class="is_show" @click="handelShow" v-if="type == 0">
        {{ showTitle }}
        <i :class="icon"></i>
      </div>
      <div style="display: none">
        <el-button @click="handelEdit" v-if="!disabled">编辑</el-button>
        <!-- <el-button type="primary" @click="handelSubmit" v-if="disabled">保存</el-button> -->
        <el-button @click="cancel" v-if="disabled">取消</el-button>
      </div>

      <div v-show="isShow">
        <el-descriptions title="授信信息" border v-if="type==1" :contentStyle="C_S">
        <!-- <el-descriptions-item label="授信单号">
          <span v-if="detailInfo.approvalStatus==7">{{detailInfo.quotaCode}}</span>
        </el-descriptions-item> -->
        <el-descriptions-item label="受理银行">{{detailInfo.bankName}}</el-descriptions-item>
        <el-descriptions-item label="授信金额（元）">{{detailInfo.actualAmt}}</el-descriptions-item>
        <el-descriptions-item label="授信期限（月）">{{detailInfo.bankTerm}}</el-descriptions-item>
        <el-descriptions-item label="利率（年化）">{{detailInfo.quotaRate}}</el-descriptions-item>
        <el-descriptions-item label="日期范围">{{detailInfo.quotaStartTime}} ～ {{detailInfo.quotaEndTime}}</el-descriptions-item>
        <!-- <el-descriptions-item label="银行地址">{{detailInfo.bankAddress}}</el-descriptions-item>
        <el-descriptions-item label="银行电话">{{detailInfo.bankContactPhone}}</el-descriptions-item>
        <el-descriptions-item label="信贷员">信贷员</el-descriptions-item> -->
      </el-descriptions>
      <!--  推荐企业和银行还没审核的时候，就没有推荐企业和银行的信息 -->
      <!-- <template v-if="detailInfo.approvalStatus != 4"> -->
        <template>
        <!-- <el-descriptions title="银行评估" border v-if="type==1" :contentStyle="CS">
        <el-descriptions-item label="银行评估额度（元）">{{detailInfo.bankAmt}}</el-descriptions-item>
      </el-descriptions> -->
      <!-- <el-descriptions title="推荐企业评估" border v-if="type==1" :contentStyle="CS">
        <el-descriptions-item label="推荐企业评估额度（元）">{{detailInfo.guarAmt}}</el-descriptions-item>
      </el-descriptions> -->
      </template>
      
      
      <!-- 。。。。。。。 -->
      <el-descriptions title="平台评估" border v-if="type==1" :contentStyle="CS">
        <el-descriptions-item label="平台评估额度（元）">{{detailInfo.platformAmt}}</el-descriptions-item>
        <el-descriptions-item label="分配银行">{{detailInfo.bankName}}</el-descriptions-item>
      </el-descriptions>
        <el-descriptions title="推荐企业信息" border v-if="detailInfo.guarId">
          <el-descriptions-item label="推荐企业名称">{{
            detailInfo.companyName
          }}</el-descriptions-item>
          <el-descriptions-item label="法人">{{
            detailInfo.corprateName
          }}</el-descriptions-item>
          <el-descriptions-item label="企业代码">{{
            detailInfo.certNo
          }}</el-descriptions-item>
        </el-descriptions>

        <el-descriptions title="申请信息" border>
          <el-descriptions-item label="申请人姓名">{{
            detailInfo.userName
          }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{
            detailInfo.userPhone
          }}</el-descriptions-item>
          <el-descriptions-item label="身份证号">{{
            detailInfo.userIdNo
          }}</el-descriptions-item>
          <el-descriptions-item label="申请额度（元）">{{
            detailInfo.applyAmt
            }}
            
          <el-tooltip effect="dark" placement="top">
            <div slot="content" v-html="tooltips"></div>
            <img style="width: 16px;height: 16px; margin-left: 10px;" src="../../../assets/images/mes/info-circle.png" />
          </el-tooltip>
          </el-descriptions-item>
          <el-descriptions-item label="申请期限（月）">
            {{detailInfo.quotaTerm}}
          </el-descriptions-item>
          <el-descriptions-item label="资金用途">{{
            detailInfo.quotaPurpose
          }}</el-descriptions-item>
          <el-descriptions-item label="申请时间">{{
            detailInfo.createTime
          }}</el-descriptions-item>
          <!-- <el-descriptions-item label="负债金额">{{
            detailInfo.debtAmt
          }}</el-descriptions-item>
          <el-descriptions-item label="负债说明">{{
            detailInfo.debtExplain
          }}</el-descriptions-item> -->
        </el-descriptions>
        <el-descriptions title="资产状况" border v-if="detailInfo.debtAmt">
          <el-descriptions-item label="负债总金额（元）">{{
            detailInfo.debtAmt
          }}</el-descriptions-item>
          <!-- <div v-for='(item, index) in debtExplain' :key="index">
            <el-descriptions-item label="借款银行">{{
              item.name
            }}</el-descriptions-item>
            <el-descriptions-item label="借款金额（元）">{{
              item.amt
            }}</el-descriptions-item>
          </div> -->

            <el-descriptions-item v-for='(item, index) in debtExplain' :key="index">
              <template slot="label">
                借款银行：{{
              item.name
            }}
              </template> 借款金额（元）：{{
              item.amt
            }}</el-descriptions-item>
        </el-descriptions>
        <div class="user_info">
          <el-descriptions title="身份信息" border>
            <el-descriptions-item label="婚姻状况" v-if="detailInfo.maritalStatus">{{detailInfo.maritalStatus | formartSex}}
            </el-descriptions-item>
            <el-descriptions-item v-if="detailInfo.maritalStatus == 2" label="配偶姓名">{{
              detailInfo.spouseName
            }}</el-descriptions-item>
            <el-descriptions-item v-if="detailInfo.maritalStatus == 2" label="配偶身份证号">{{
              detailInfo.spouseIdNo
            }}</el-descriptions-item>
            <el-descriptions-item v-if="detailInfo.maritalStatus != 1" label="是否有子女">{{
              detailInfo.childFlag == 1 ? '是' : '否'
            }}</el-descriptions-item>
            <el-descriptions-item v-if="detailInfo.maritalStatus != 1" label="子女情况">{{
              handelChildSituation(childSituation, detailInfo.childInfo)
            }}</el-descriptions-item>
          </el-descriptions>

        </div>
          
        <div class="user_card">
          <span class="user_title" v-if="faImageList.length > 0">申请方证件信息</span>
          <div class="images" v-if="faImageList.length > 0">
            <div class="view-images">
              <div
                class="view-images-list"
                v-for="(imgItem, index) in faImageList"
                :key="index"
              >
                <img :src="imgItem.url" alt="" @click="showimage(imgItem)" />
              </div>
            </div>
          </div>
          <div class="user_card" v-if="detailInfo.maritalStatus == 2">
            <span class="user_title">配偶身份证信息</span>
            <div class="images">
              <div class="view-images" v-if="wifeList.length>0">
                <div
                  class="view-images-list"
                  v-for="(imgItem, index) in wifeList"
                  :key="index"
                >
                  <!-- <div
                    class="delete-img"
                    v-if="disabled"
                    @click="deleteWifeImg(index, wifeList)"
                  >
                    <i class="el-icon-error"></i>
                  </div> -->
                  <img
                    :src="imgItem.url"
                    alt=""
                    @click="showWifeImage(imgItem)"
                  />
                </div>
              </div>
            </div>
          </div>

       <!-- <el-descriptions title="征信信息" border direction="vertical">
          <el-descriptions-item label="申请人征信信息" :span="3">
            <div class="images" style="display: flex">
              <div class="view-images">
                <div
                  v-for="(item, index) in detailInfo.quotaFileList"
                  :key="item.id"
                >
                  <div class="view-images-list" v-if="item.fileType == 1">
                    <div
                      class="delete-img"
                      v-if="commonFlay"
                      @click="deleteApply(index, detailInfo.quotaFileList)"
                    >
                      <i class="el-icon-error"></i>
                    </div>
                    <img
                      v-if="item.fileType == 1"
                      style="width: 150px; height: 150px; margin-right: 10px"
                      :src="item.filePath"
                      alt=""
                      @click="showApplyImage(item)"
                    />
                  </div>
                </div>
              </div>
            </div>
            <el-dialog :visible.sync="dialogVisible">
              <img width="100%" :src="dialogImageUrl" alt="" />
            </el-dialog>
          </el-descriptions-item>
          <el-descriptions-item label="配偶征信信息" :span="3">
            <div class="images" style="display: flex">
              <div class="view-images">
                <div
                  v-for="(item, index) in detailInfo.quotaFileList"
                  :key="item.id"
                >
                  <div class="view-images-list" v-if="item.fileType == 2">
                    <div
                      class="delete-img"
                      v-if="commonFlay"
                      @click="deleteApplyWife(index, detailInfo.quotaFileList)"
                    >
                      <i class="el-icon-error"></i>
                    </div>
                    <img
                      v-if="item.fileType == 2"
                      style="width: 150px; height: 150px; margin-right: 10px"
                      :src="item.filePath"
                      alt=""
                      @click="showApplyImageWife(item)"
                    />
                  </div>
                </div>
              </div>
            </div>
          </el-descriptions-item>
        </el-descriptions>

        <el-descriptions title="流水信息" border direction="vertical">
          <el-descriptions-item label="申请人近12个月银行流水记录" :span="3">
            <div class="images" style="display: flex">
              <div class="view-images">
                <div
                  v-for="(item, index) in detailInfo.quotaFileList"
                  :key="item.id"
                >
                  <div class="view-images-list" v-if="item.fileType == 3">
                    <div
                      class="delete-img"
                      v-if="commonFlay"
                      @click="deleteFlow(index, detailInfo.quotaFileList)"
                    >
                      <i class="el-icon-error"></i>
                    </div>
                    <img
                      v-if="item.fileType == 3"
                      style="width: 150px; height: 150px; margin-right: 10px"
                      :src="item.filePath"
                      alt=""
                      @click="showApplyFlow(item)"
                    />
                  </div>
                </div>
              </div>
            </div>
          </el-descriptions-item>
        </el-descriptions> -->
        <div class="user_info">
          <el-descriptions title="订单信息" border v-if="detailInfo.orderInfo">
            <el-descriptions-item label="收款方全称">{{
              detailInfo.companyName
            }}</el-descriptions-item>
            <el-descriptions-item label="订单编号">{{
              detailInfo.userPhone
            }}</el-descriptions-item>
            <el-descriptions-item label="支付方式">预付款 + 尾款</el-descriptions-item>
            <el-descriptions-item label="订单金额（元）">{{
              detailInfo.applyAmt
            }}</el-descriptions-item>
            <el-descriptions-item label="下单时间">{{
              detailInfo.createTime
            }}</el-descriptions-item>
          </el-descriptions>
        <el-table :data="dataOrderInfoList" v-if="detailInfo.orderInfo" highlight-current-row >
          <el-table-column align="center" label="商品名称" prop="goodsName" fixed />
          <el-table-column align="center" label="单价" prop="singlePrice" fixed />
          <el-table-column align="center" label="数量" prop="goodsNum" fixed />
        </el-table>
        </div>
        <div class="user_info">
          <el-descriptions title="经营信息" border :column="3" :contentStyle="CS">
            <el-descriptions-item label="经营场所地址">
              {{detailInfo.businessPremisesName}} {{detailInfo.address}}
              </el-descriptions-item>
              <!-- <el-descriptions-item label="养殖数量">
              {{detailInfo.numOfBreeding}}
              </el-descriptions-item> -->
             <el-descriptions-item label="牛数量（头）">{{detailInfo.cattleNum}}</el-descriptions-item>
            <el-descriptions-item label="羊数量（只）">{{detailInfo.sheepNum}}</el-descriptions-item>
            <!-- <el-descriptions-item label="其它活畜数量">{{detailInfo.otherNum}}</el-descriptions-item> -->
            <el-descriptions-item label="自有草场（亩）">{{detailInfo.ownPasture}}</el-descriptions-item>
            <el-descriptions-item label="租用草场（亩）">{{detailInfo.rentPasture}}</el-descriptions-item>
            <el-descriptions-item label="棚圈面积（㎡）">{{detailInfo.shedArea}}</el-descriptions-item>
            <el-descriptions-item label="房屋面积（㎡）">{{detailInfo.housesArea}}</el-descriptions-item>
            <el-descriptions-item label="预计所需饲料（吨）">{{detailInfo.fodderTon}}</el-descriptions-item>
            <el-descriptions-item label="预计所需饲草（吨）">{{detailInfo.forageTon}}</el-descriptions-item>
            <!-- <el-descriptions-item label="棚圈（间）">{{detailInfo.shedNum}}</el-descriptions-item>
            <el-descriptions-item label="棚圈总价值（万）">{{detailInfo.shedAmtWan}}</el-descriptions-item>
            <el-descriptions-item label="房屋（平米）">{{detailInfo.housesArea}}</el-descriptions-item>
            <el-descriptions-item label="房屋价值（万）">{{detailInfo.housesAmtWan}}</el-descriptions-item>
            <el-descriptions-item label="生产机械（台）">{{detailInfo.prodMachinery}}</el-descriptions-item>
            <el-descriptions-item label="生产机械总价值（万）">{{detailInfo.prodMachineryAmtWan}}</el-descriptions-item>
            <el-descriptions-item label="其它财产信息">{{detailInfo.otherPropertyInfo}}</el-descriptions-item>
            <el-descriptions-item label="其它财产信息总价值 （万）">{{detailInfo.otherPropertyInfoAmtWan}}</el-descriptions-item> -->
          </el-descriptions>
          <div class="user_card">
            <span class="user_title">棚圈照片</span>
            <div class="images" style="display: flex">
              <div class="view-images">
                <div
                  v-for="(item, index) in shedPic"
                  :key="index"
                >
                  <div class="view-images-list">
                    <div
                      class="delete-img"
                      v-if="commonFlay"
                      @click="deleteAddress(index, shedPic)"
                    >
                      <i class="el-icon-error"></i>
                    </div>
                    <img
                     
                      style="width: 150px; height: 150px; margin-right: 10px"
                      :src="item"
                      alt=""
                      @click="showApplyAddress(item)"
                    />
                  </div>
                </div>
                <!-- Flow -->
              </div>
            </div>
          </div>
          <div class="user_card">
            <span class="user_title">养殖活畜照片</span>
            <div class="images" style="display: flex">
              <div class="view-images">
                <div
                  v-for="(item, index) in livestockPic"
                  :key="index"
                >
                  <div class="view-images-list">
                    <div
                      class="delete-img"
                      v-if="commonFlay"
                      @click="deleteAnimal(index, livestockPic)"
                    >
                      <i class="el-icon-error"></i>
                    </div>
                    <img
                     
                      style="width: 150px; height: 150px; margin-right: 10px"
                      :src="item"
                      alt=""
                      @click="showApplyAnimal(item)"
                    />
                  </div>
                </div>
                <!-- Flow -->
              </div>
            </div>
          </div>
          <div class="user_card">
            <span class="user_title">申请人房屋照片</span>
            <div class="images" style="display:flex">
                <div class="view-images">
                  <div v-for="(item, index) in housesPic" :key="index">
                    <div class="view-images-list">
                      <img style="width:150px; height:150px;margin-right:10px" :src="item" alt="" @click="showApplyHouse(item)">
                    </div>
                  </div>
                 </div>  
              </div>
          </div>

          <!--  征信照片-->
         <div class="user_card">
            <span class="user_title">征信照片</span>
            <div class="images" style="display:flex">
                <div class="view-images">
                  <div v-for="(item,index) in creditPic" :key="index">
                    <div class="view-images-list">
                      <img style="width:150px; height:150px;margin-right:10px" :src="item" alt="" @click="showCreditPic(item)">
                    </div>
                  </div>
                </div>
            </div>
          </div>
          <div class="user_card">
            <span class="user_title">其它照片</span>
            <div class="images" style="display:flex">
                <div class="view-images">
                  <div v-for="(item, index) in otherPic" :key="index">
                    <div class="view-images-list">
                      <img style="width:150px; height:150px;margin-right:10px" :src="item" alt="" @click="showApplyOther(item)">
                    </div>
                  </div>
                 </div>  
              </div>
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      title=""
      :visible.sync="dialogVisible"
    >
      <img style="width:100%" :src="dialogImageUrl" alt="">
    </el-dialog>

    <el-button v-if="detailInfo.approvalStatus == 4 && type == 1" type="primary" @click="changeType">去审核</el-button>
  </div>
  </div>
</template>

<script>
import baseInfo from "../components/baseInfo/index.vue"
import { queryApplyDetail, changeApproStatus, selectBank } from "@/api/nlbao/guarantee"
import { basicPath2 } from "@/api/base.js"
import { getToken } from "@/utils/auth"
import axios from "axios"
import { getDicts } from "@/api/system/dict/data.js";

export default {
  dicts: ["nlbao_bank_time"],
  components: {
    baseInfo
  },
  data() {
    return {
      dataTime: '',
      input: "信贷员",
      isShow: false,
      showTitle: "点击查看申请详情",
      icon: "el-icon-arrow-down",
      ruleForm: {
        actualAmt: null,
        bankAddress: "",
        bankUserId: '',
        applyType: '',
        orderAmountRate: '100'
      },
      disBank: 1,
      shedPic: [],
      livestockPic: [],
      housesPic: [],
      otherPic: [],
      creditPic: [],
      certificatePic: [],
      applyTypeList: [
          { text: '活畜抵押', value: 1 },
          { text: '信用额度', value: 2 },
          { text: '无货质押', value: 3 },
          { text: '企业推荐', value: 4 },
          { text: '仓单质押', value: 5 },
          { text: '粮仓无货质押', value: 6 },
            { label: '粮仓仓单质押', value: 7 },
      ],
      rules: {
        approvalStatus: [
          { required: true, message: "请选择审核意见", trigger: "change" }
        ],
        bankAmt: [
          { required: true, message: "请输入评估额度", trigger: "blur" },
          
           {pattern: /(?:^[1-9]([0-9]+)?(?:\.[0-9]{1,2})?$)|(?:^(?:0)$)|(?:^[0-9]\.[0-9](?:[0-9])?$)/, message: '请输入正确的评估额度', trigger: 'blur'}
        ],
        actualAmt: [
          { required: true, message: "请输入授信金额", trigger: "blur" },
          {pattern: /(?:^[1-9]([0-9]+)?(?:\.[0-9]{1,2})?$)|(?:^(?:0)$)|(?:^[0-9]\.[0-9](?:[0-9])?$)/, message: '请输入正确的授信金额', trigger: 'blur'}
        ],
        bankTerm: [
          { required: true, message: "请选择授信期限", trigger: "change" }
        ],
        quotaRate: [
          { required: true, message: "请输入利率", trigger: "blur" },
          {pattern: /(?:^[1-9]([0-9]+)?(?:\.[0-9]{1,2})?$)|(?:^(?:0)$)|(?:^[0-9]\.[0-9](?:[0-9])?$)/, message: '利率小数点不能超过两位', trigger: 'blur'}
        ],
        dataTime: [
          // { type: 'date', required: true, message: '请选择授信起始日期', trigger: 'change' }
        ],
        orderAmountRate: [
          { required: true, message: '请输入使用比例', trigger: 'blur' },
          {pattern: /(?:^[1-9]([0-9]+)?(?:\.[0-9]{1,2})?$)|(?:^(?:0)$)|(?:^[0-9]\.[0-9](?:[0-9])?$)/, message: '比例小数点不能超过两位', trigger: 'blur'}
        ],
      },
      uploadImgUrl:
        process.env.VUE_APP_BASE_API + `${basicPath2}files/obs/fileUpload`, // 上传的图片服务器地址
      headers: {
        Authorization: getToken()
      },
      orderId: "",
      active: undefined,
      orderInfo: {},
      orderData: [],
      id: "",
      reasonOther:'',
      checkOptions: [{
        value: '信息填写不完善',
        label: '信息填写不完善'
      }, {
        value: '信息填写有误',
        label: '信息填写有误'
      }, {
        value: '资料存在不真实性',
        label: '资料存在不真实性'
      }, {
        value: '所在地区无法办理',
        label: '所在地区无法办理'
      }, {
        value: '其他',
        label: '其他'
      }],
      detailInfo: {},
      dialogVisible: false,
      dialogImageUrl: "",
      hideUpload: true,
      // 法人身份证信息
      faImageList: [],
      envflag: false,
      maxCount: 2, //证件
      // 配偶身份证信息
      wifeList: [],
      wifeFlag: false,
      wifeCount: 2,
      // 申请人征信
      quotaFileList: [],
      commonFlay: false,
      imgUrl: "",
      disabled: false,
      marketAddressid: [],
      addressOptions: [],
      province: "",
      city: "",
      area: "",
      detailAdd: "",
      armtList: [],
      minArmt: undefined,
      type: "",
      status: false,
      currentUserInfo: {},
      subBankList: [],
      currentUserId: "",
      bankContactPhone: '',
      isQankTermSelect: false,
      otherTime: '',
      debtExplain: [],
      isDisabled: {
        // disabledDate: (time) =>{
        //   // 最大日期为当前日期
        //   let myDate = new Date();
        //   let maxDay = myDate.setDate(new Date().getDate() - 1);		
        //   return time.getTime() <= maxDay;
        // }
      },
      endDate: '',
      dateRange: '',
      CS: {
        width: "200px", //最小宽度
        "word-break": "break-all", //过长时自动换行
      },
      C_S: {
        width: "280px", //最小宽度
        "word-break": "break-all" //过长时自动换行
      },
      currentUserInfoCustomer: {},
      dataOrderInfoList: [],
      childSituation: [
          { name: '上学', value: 1 },
          { name: '私企上班', value: 2 },
          { name: '公职人员', value: 3 },
          { name: '牧业', value: 4 },
          { name: '个体', value: 5 },
          { name: '无业', value: 6 }
      ],
      tooltips: ''
    }
  },
   filters: {            
    // 状态 (0取消 1提交成功，2畜牧师成功，3运营人成功，4推荐企业成功，5银行成功)
    formartSex: (type) =>{
      const statusMap = {
        1: '未婚',
        2: '已婚',
        3: '离异',
        4: '丧偶',
      }
      return statusMap[type]
    }
  },
  computed: {
    handelChildSituation() {
      return (list, value) => {
        let name = "";
        list.forEach((item) => {
          if (item.value == value) {
            name = item.name;
          }
        });
        return name;
      };
    },
  },
  created() {
    this.getAddressList()
    this.id = this.$route.query.id
    this.getQueryApplyDetail()
    this.type = this.$route.query.type
    this.type == 1 ? (this.isShow = true) : (this.isShow = false)
    const user_info = JSON.parse(window.localStorage.getItem("extsysUserInfo"))
    this.currentUserInfo = user_info
    this.currentUserInfoCustomer = user_info.customer
    this.getBankList()
  },
  mounted() {},
  methods: {
    changeInputRate(val){
      const rateVal = Number(val);
       if(rateVal > 100) {
         this.$message.error('利率不能超过100')
       }
    },
    handelChangeDate() {
      if(this.ruleForm.bankTerm) {
        this.bankEndTime(this.dataTime, Number(this.ruleForm.bankTerm))
      }
    },
    handelChangeRangeDate(){
      if (this.ruleForm.bankTerm == -50) {
        this.isQankTermSelect = true
      } else {
        this.isQankTermSelect = false
      }
      if(this.dataTime){
        this.bankEndTime(this.dataTime, Number(this.ruleForm.bankTerm))
      }
    },
    changeotherTime() {
      if(this.dataTime){
        this.bankEndTime(this.dataTime, Number(this.otherTime))
      }
    },
    // 审核详情展示
    handelChangeStatus() {
      this.ruleForm.approvalStatus == 5
        ? (this.status = true)
        : (this.status = false)
        this.ruleForm.reason = ''
        this.ruleForm = JSON.parse(JSON.stringify(this.ruleForm))
        this.reasonOther = ''
    },
    changeType() {
      this.type = 0
      this.type == 1 ? (this.isShow = true) : (this.isShow = false)
    },
    getBankList() {
        selectBank({
            pageNum: 1,
            pageSize: 1000,
            parentUserId: this.currentUserInfoCustomer.level == 2 ? this.currentUserInfo.access_token : ''
        }).then((res) => {
          if (res.stautscode == 200) {
              const data = res.data;
              data.list.map(item => {
                  item.text = item.companyName;
                  item.value = item.userId;
                  return item;
              });
              this.subBankList = data.list
          } else{
            this.$message.error(res.msg);
          }
        })
    },
    // 获取地址信息
    async getAddressList() {
      const { data: res } = await axios.get("/city.json")
      this.addressOptions = res
    },
    async getQueryApplyDetail() {
      const { data: res } = await queryApplyDetail({ quotaId: this.id })
      this.detailInfo = res
      this.shedPic = this.detailInfo.shedPic && this.detailInfo.shedPic.split(',');
      this.livestockPic = this.detailInfo.livestockPic && this.detailInfo.livestockPic.split(',');
      this.housesPic = this.detailInfo.housesPic && this.detailInfo.housesPic.split(',');
      this.otherPic = this.detailInfo.otherPic && this.detailInfo.otherPic.split(',');
      this.creditPic = this.detailInfo.creditPic && this.detailInfo.creditPic.split(',');
      this.active = res.approvalStatus
      this.debtExplain = JSON.parse(this.detailInfo.debtExplain)
      if (this.detailInfo.orderInfo) {
        this.dataOrderInfoList = JSON.parse(this.detailInfo.orderInfo).goodsModel
      }
      // applyAmt 申请金额
      // platformAmt  平台金额
      // bankAmt   银行金额
      // guarAmt 推荐企业
      this.armtList.push(
        res.applyAmt,
        res.platformAmt,
        // res.bankAmt,
      )
      // if (res.guarId) {
      //   this.armtList.push(res.guarAmt)
      // }
      console.log(this.armtList)
      // let addressArr = res.businessPremises.split(",")
      // this.marketAddressid = addressArr
      const bankAddress = res?.bankAddress? res?.bankAddress : ''
      this.bankContactPhone =res.bankContactPhone
      // const addressDetail = res?.address ?res?.address:''
      // this.ruleForm.bankAddress = res?.bankAddress + res?.address
      this.ruleForm.bankAddress = bankAddress
      // const minArmt = Math.min(...this.armtList)
      const minArmt = this.armtList.reduce((x, y) => x < y ? x : y);
      this.ruleForm.actualAmt = minArmt
      this.minArmt = minArmt
      this.$forceUpdate()

      
    if (this.detailInfo.userCertPic1) {
      this.faImageList.push({
        url: this.detailInfo.userCertPic1,
        type:1
      })
    }
    if (this.detailInfo.userCertPic2) {
      this.faImageList.push({
        url: this.detailInfo.userCertPic2,
        type:2
      })
    }
     /*  this.wifeList.push({
        url: this.detailInfo.spouseIdPic1
      })
      this.wifeList.push({
        url: this.detailInfo.spouseIdPic2
      }) */
        if(this.detailInfo.spouseIdPic1) {
        this.wifeList.push({
        url: this.detailInfo.spouseIdPic1
      })
      }
      if(this.detailInfo.spouseIdPic2) {
         this.wifeList.push({
        url: this.detailInfo.spouseIdPic2
      })
      }
     getDicts('nyb_quota_fodder_conf').then((res) => {
        const fodderConf =res.data || []
          let sheepValue = 0;
          let cattleValue = 0;
          let sheepPrice = 0;
          let cattlePrice = 0;
          fodderConf.forEach(item => {
            if(item.dictLabel == 1) {
              sheepValue = item.dictValue * this.detailInfo.sheepNum
              sheepPrice = item.dictValue
            }
            if(item.dictLabel == 2){
              cattleValue = item.dictValue * this.detailInfo.cattleNum
              cattlePrice = item.dictValue
            }
          })
        setTimeout(() => {
          const totals = (+cattleValue + +sheepValue).toFixed(2)
          this.tooltips = `
            该用户饲草料年需求金预计为${totals}元
            <br/>
            计算规则：牛只一年一头预计消耗饲草料${cattlePrice}元，
                    羊只一年一只预计消耗饲草料${sheepPrice}元，
                    申请人当前牛只存栏${this.detailInfo.cattleNum}头，羊只存栏${this.detailInfo.sheepNum}只，
                    合计金额=${cattlePrice}*${this.detailInfo.cattleNum}+${sheepPrice}*${this.detailInfo.sheepNum}=${totals}元
          `
        }, 3000)
     })
    },
    // 申请人及配偶身份证上传
    deleteimg(index, fileList) {
      this.$nextTick(() => {
        fileList.splice(index, 1)
        this.faImageList = fileList
        this.envflag = !(fileList.length >= this.maxCount)
      })
    },
    showimage(file) {
      console.log(file)
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    handlEnvironmentSuccess(res, file, fileList) {
      this.faImageList.push({
        url: res.result[0].objectUrl
      })
      this.$nextTick(() => {
        this.envflag = !(fileList.length >= this.maxCount)
      })
    },
    deleteWifeImg(index, fileList) {
      fileList.splice(index, 1)
      this.wifeList = fileList
      this.$nextTick(() => {
        this.wifeFlag = !(fileList.length >= this.maxCount)
      })
    },
    showWifeImage(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    handlWifeSuccess(res, file, fileList) {
      this.wifeList.push({
        url: res.result[0].objectUrl
      })
      this.$nextTick(() => {
        this.wifeFlag = !(fileList.length >= this.maxCount)
      })
    },
    handlePictureCardPreview() {},
    handlSuccessCreditPic(res, file, fileList) {
      this.certificatePic.push(file.response.result[0].objectUrl)
    },
    showertificatePic(file) {
      this.dialogImageUrl = file
      this.dialogVisible = true
    },
    deleteCertificatePic(index, fileList) {
      fileList.splice(index, 1)
      this.certificatePic = fileList
    },
    // 申请人征信上传
    deleteApply(index, fileList) {
      console.log(index)
      console.log(fileList)
      fileList.splice(index, 1)
      this.detailInfo.quotaFileList = fileList
    },
    showApplyImage(file) {
      console.log(file)
      this.dialogImageUrl = file.filePath
      this.dialogVisible = true
    },
    handlSuccessApply(res, file, fileList) {
      this.detailInfo.quotaFileList.push({
        fileType: 1,
        fileName: file.url,
        filePath: res.result[0].objectUrl
      })
    },
    // 配偶征信信息
    deleteApplyWife(index, fileList) {
      console.log(index)
      console.log(fileList)
      fileList.splice(index, 1)
      this.detailInfo.quotaFileList = fileList
    },
    showApplyImageWife(file) {
      console.log(file)
      this.dialogImageUrl = file.filePath
      this.dialogVisible = true
    },
    showApplyHouse(file) {
      this.dialogImageUrl = file
      this.dialogVisible = true
    },
    showApplyOther(file) {
      this.dialogImageUrl = file
      this.dialogVisible = true
    },
    showCreditPic(file) {
      this.dialogImageUrl = file
      this.dialogVisible = true
    },
    handlSuccessApplyWife(res, file, fileList) {
      this.detailInfo.quotaFileList.push({
        fileType: 2,
        fileName: file.url,
        filePath: res.result[0].objectUrl
      })
    },
    // 申请人近12个月银行流水记录  
      deleteFlow(index, fileList) {
      console.log(index)
      console.log(fileList)
      fileList.splice(index, 1)
      this.detailInfo.quotaFileList = fileList
    },
    showApplyFlow(file) {
      console.log(file)
      this.dialogImageUrl = file.filePath
      this.dialogVisible = true
    },
    handlSuccessFlow(res, file, fileList) {
      this.detailInfo.quotaFileList.push({
        fileType: 3,
        fileName: file.url,
        filePath: res.result[0].objectUrl
      })
    },
    // 经营场所场地照片
    deleteAddress(index, fileList) {
      fileList.splice(index, 1)
      this.detailInfo.quotaFileList = fileList
    },
    showApplyAddress(file) {
      this.dialogImageUrl = file
      this.dialogVisible = true
    },
    handlSuccessAddress(res, file, fileList) {
      this.detailInfo.quotaFileList.push({
        fileType: 4,
        fileName: file.url,
        filePath: res.result[0].objectUrl
      })
    },
    // 养殖活畜照片
    deleteAnimal(index, fileList) {
      fileList.splice(index, 1)
      this.detailInfo.quotaFileList = fileList
    },
    showApplyAnimal(file) {
      console.log(file)
      this.dialogImageUrl = file
      this.dialogVisible = true
    },
    handlSuccessAnimal(res, file, fileList) {
      this.detailInfo.quotaFileList.push({
        fileType: 5,
        fileName: file.url,
        filePath: res.result[0].objectUrl
      })
    },
    cancel() {
      this.commonFlay = false
      this.disabled = false
      this.envflag = false
      this.wifeFlag = false
    },
    changeInput() {
      console.log(Number(this.minArmt) , Number(this.ruleForm.actualAmt)) 
      if (Number(this.minArmt) < Number(this.ruleForm.actualAmt)) {
        this.$message.info("授信金额不能超过最低金额")
      }
    },
    // 授信结束时间
    // bankEndTime(yearMonthDay, monthNum){
    //    let arr = yearMonthDay.split('-');
    //     let year = parseInt(arr[0]);
    //     let month = parseInt(arr[1]);
    //     let currentDate = yearMonthDay;
    //     let d = new Date(currentDate);
    //     // let day = new Date(d.setDate(d.getDate()));
    //      d.setDate(d.getDate() - 1);
    //     //  console.log(day)
    //     // day.toLocaleDateString();
    //     let dd = d.getDate()
    //     console.log(d.getMonth()-1)
    //     dd = dd < 10 ? ('0' + dd) : dd
    //     month = month + monthNum;
    //     if(month > 12){//月份加
    //         let yearNum = parseInt((month-1)/12);
    //         month = month % 12 == 0 ? 12 : month % 12;
    //         year += yearNum;
    //     }else if(month <= 0){//月份减
    //         month = Math.abs(month);
    //         let yearNum = parseInt((month+12) / 12);
    //         year -= yearNum;
    //     }
    //     console.log(month);
    //     month = month < 10 ? ('0' + month ): month;
    //     let lastDate = year+"-"+ month +"-"+ dd;
    //     this.endDate = lastDate
    // },
    bankEndTime(date, month) {
      const day = new Date(date)
        day.setMonth(day.getMonth() + month);
        day.setDate(day.getDate() - 1);
        day.toLocaleDateString()
        let y = day.getFullYear()
        let m = day.getMonth() + 1
        m = m < 10 ? ('0' + m) : m
        let d = day.getDate()
        d = d < 10 ? ('0' + d) : d
        const time = y + '-' + m + '-' + d;
        this.endDate = time
    },
    handelSubmit() {
      this.$refs.ruleForm.validate((valid) => {
          if (valid) {
            if (this.status) {
        if (Number(this.minArmt) < Number(this.ruleForm.actualAmt)) {
          this.$message.info("授信金额不能超过最低金额")
        } else if(Number(this.ruleForm.quotaRate)>100) {
          this.$message.error('利率不能超过100')
        } else if(Number(this.ruleForm.orderAmountRate)>100) {
          this.$message.error('使用比例不能超过100')
        } else {
          const quotaStartTime = this.dataTime ? this.dataTime : ""
          const quotaEndTime = this.endDate ? this.endDate : ""
          const passParams = {
            id: Number(this.id),
            ...this.ruleForm,
            bankUserId: this.currentUserInfo.access_token,
            quotaStartTime: quotaStartTime,
            quotaEndTime: quotaEndTime,
            quotaPic: this.certificatePic && this.certificatePic.join(',')
          }
          if (this.ruleForm.bankTerm == -50) {
            passParams.bankTerm = this.otherTime
          }

          if (this.ruleForm.reason == '其他') {
            passParams.reason = this.reasonOther
          }
          // return
          changeApproStatus(passParams).then((res) => {
            if (res.stautscode == 200) {
              this.$message.success("审核成功")
              this.commonFlay = false
              this.disabled = false
              this.envflag = false
              this.wifeFlag = false
              this.$router.go(-1)
            } else{
              this.$message.error(res.msg);
            }
            
          })
        }
      } else {
        console.log("butongg")
        this.$refs.ruleForm.validate((valid) => {
          if (valid) {
            const params = {
              id: Number(this.id),
              approvalStatus: this.ruleForm.approvalStatus,
              reason: this.ruleForm.reason
            }
            if (this.ruleForm.reason == '其他') {
              params.reason = this.reasonOther
            }
            if (this.ruleForm.approvalStatus === 6) {
              if (!this.ruleForm.reason) {
                this.$message.error("审核意见不能为空")
              } else {
                changeApproStatus(params).then((res) => {
                  if (res.stautscode == 200) {
                    this.$message.success("驳回成功")
                    this.commonFlay = false
                    this.disabled = false
                    this.envflag = false
                    this.wifeFlag = false;
                    this.$router.go(-1)
                  } else {
                    this.$message.error(res.msg)
                    this.$router.go(-1)
                  }
                })
              }
            }
          }
        })
      }
          }
      })
      
    },
    handelSubmit1() {
      changeApproStatus({
          id: this.$route.query.id,
          bankUserId: this.ruleForm.bankUserId,
          approvalStatus: 41
      }).then((res) => {
          if (res.stautscode == 200) {
            this.$message.success("分配成功")
            this.commonFlay = false
            this.disabled = false
            this.envflag = false
            this.wifeFlag = false
            this.$router.go(-1)
          } else{
            this.$message.error(res.msg);
          }
          
        })
      
    },
    handelShow() {
      this.isShow = !this.isShow
      this.isShow == true
        ? (this.showTitle = "收起申请详情")
        : (this.showTitle = "展开查看申请详情")
      this.isShow == true
        ? (this.icon = "el-icon-arrow-up")
        : (this.icon = "el-icon-arrow-down")
    },

    // 启用/禁用编辑
    handelEdit() {
      this.disabled = true
      this.hideUpload = false
      this.commonFlay = true
    },

    // 三级地址选择
    handleChange(value) {
      console.log(value)
      if (value && value.length != 0) {
        let arr = this.$refs["cascaderAddr"].getCheckedNodes()[0].pathLabels
        console.log(arr)
        this.detailAdd = arr.join(",")
        this.province = arr[0]
        this.city = arr[1]
        this.area = arr[2]
      }
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
::v-deep input[type="number"] {
  -moz-appearance: textfield;
}
.is_show {
  width: 100%;
  text-align: center;
  color: rgb(22, 155, 213);
  cursor: pointer;
}
::v-deep .el-steps--horizontal {
  width: 80%;
}
.step_style {
  width: 100%;
  display: flex;
  justify-content: center;
  margin: 10px 0;
}
::v-deep .el-descriptions__header {
  background-color: rgb(242, 242, 242);
  padding: 0 20px;
  margin: 30px 0 10px 0;
  height: 40px;
}

.user_info {
  .user_card {
    .user_title {
      margin: 10px 0;
      display: inline-block;
      color: rgb(153, 153, 153);
      font-size: 14px;
    }
    .images {
      display: flex;
      flex-wrap: wrap;
      .el-image {
        width: 150px;
        height: 150px;
        margin-right: 20px;
      }
    }
  }
}
.step_style {
  width: 100%;
  margin: 10px 0;
}
.view-images {
  display: flex;
  flex-wrap: wrap;
  .view-images-list {
    width: 146px;
    height: 146px;
    display: inline-block;
    margin: 0 10px 0 10px;
    position: relative;
    .delete-img {
      display: inline-block;
      font-size: 24px;
      cursor: pointer;
      color: rgba(0, 0, 0, 0.5);
      position: absolute;
      top: -16px;
      right: -9px;
    }
    img {
      width: 100%;
      height: 100%;
      border-radius: 10px;
      cursor: pointer;
    }
  }
}
</style>
